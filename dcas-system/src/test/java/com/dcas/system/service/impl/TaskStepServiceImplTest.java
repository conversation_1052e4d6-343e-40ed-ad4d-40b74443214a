package com.dcas.system.service.impl;

import cn.hutool.json.JSONUtil;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.other.InParam;
import com.dcas.common.model.vo.IntegrationFormFieldVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TaskStepServiceImpl 参数类型验证测试
 */
@ExtendWith(MockitoExtension.class)
class TaskStepServiceImplTest {

    @InjectMocks
    private TaskStepServiceImpl taskStepService;

    private Method validateParameterTypesMethod;
    private Method isArrayValueMethod;
    private Method expectsSingleValueMethod;

    @BeforeEach
    void setUp() throws Exception {
        // 使用反射获取私有方法进行测试
        validateParameterTypesMethod = TaskStepServiceImpl.class.getDeclaredMethod(
                "validateParameterTypes", List.class, List.class, Long.class, String.class);
        validateParameterTypesMethod.setAccessible(true);

        isArrayValueMethod = TaskStepServiceImpl.class.getDeclaredMethod("isArrayValue", String.class);
        isArrayValueMethod.setAccessible(true);

        expectsSingleValueMethod = TaskStepServiceImpl.class.getDeclaredMethod("expectsSingleValue", String.class);
        expectsSingleValueMethod.setAccessible(true);
    }

    @Test
    void testIsArrayValue_WithJsonArray() throws Exception {
        // 测试JSON数组格式
        String jsonArrayValue = "[{\"id\":1,\"name\":\"test1\"},{\"id\":2,\"name\":\"test2\"}]";
        Boolean result = (Boolean) isArrayValueMethod.invoke(taskStepService, jsonArrayValue);
        assertTrue(result, "应该识别JSON数组格式");
    }

    @Test
    void testIsArrayValue_WithSingleValue() throws Exception {
        // 测试单个值
        String singleValue = "test_value";
        Boolean result = (Boolean) isArrayValueMethod.invoke(taskStepService, singleValue);
        assertFalse(result, "应该识别单个值");
    }

    @Test
    void testExpectsSingleValue_WithBasicTypes() throws Exception {
        // 测试基本数据类型
        String[] basicTypes = {"string", "integer", "int", "long", "double", "float", "boolean"};
        
        for (String type : basicTypes) {
            Boolean result = (Boolean) expectsSingleValueMethod.invoke(taskStepService, type);
            assertTrue(result, "基本类型 " + type + " 应该期望单个值");
        }
    }

    @Test
    void testValidateParameterTypes_TypeMismatch_ShouldThrowException() throws Exception {
        // 准备测试数据 - 数组值但期望单个值
        IntegrationFormFieldVO arrayField = new IntegrationFormFieldVO();
        arrayField.setName("testField");
        arrayField.setValue("[{\"id\":1},{\"id\":2}]"); // 数组值
        
        InParam.ColumnParam singleParam = new InParam.ColumnParam();
        singleParam.setColumnName("testField");
        singleParam.setColumnType("integer"); // 期望单个整数
        
        List<IntegrationFormFieldVO> convertedFields = Arrays.asList(arrayField);
        List<InParam.ColumnParam> saveParams = Arrays.asList(singleParam);
        
        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            try {
                validateParameterTypesMethod.invoke(taskStepService, convertedFields, saveParams, 1L, "测试步骤");
            } catch (Exception e) {
                if (e.getCause() instanceof ServiceException) {
                    throw (ServiceException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });
        
        assertTrue(exception.getMessage().contains("参数类型不匹配"));
        assertTrue(exception.getMessage().contains("testField"));
        assertTrue(exception.getMessage().contains("数组类型的值"));
        assertTrue(exception.getMessage().contains("期望单个"));
    }

    @Test
    void testValidateParameterTypes_ValidSingleValue_ShouldPass() throws Exception {
        // 准备测试数据 - 单个值且类型匹配
        IntegrationFormFieldVO singleField = new IntegrationFormFieldVO();
        singleField.setName("testField");
        singleField.setValue("123"); // 单个整数值
        
        InParam.ColumnParam singleParam = new InParam.ColumnParam();
        singleParam.setColumnName("testField");
        singleParam.setColumnType("integer"); // 期望单个整数
        
        List<IntegrationFormFieldVO> convertedFields = Arrays.asList(singleField);
        List<InParam.ColumnParam> saveParams = Arrays.asList(singleParam);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            try {
                validateParameterTypesMethod.invoke(taskStepService, convertedFields, saveParams, 1L, "测试步骤");
            } catch (Exception e) {
                if (e.getCause() instanceof RuntimeException) {
                    throw (RuntimeException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });
    }

    @Test
    void testValidateParameterTypes_EmptyValue_ShouldPass() throws Exception {
        // 准备测试数据 - 空值应该跳过验证
        IntegrationFormFieldVO emptyField = new IntegrationFormFieldVO();
        emptyField.setName("testField");
        emptyField.setValue(""); // 空值
        
        InParam.ColumnParam singleParam = new InParam.ColumnParam();
        singleParam.setColumnName("testField");
        singleParam.setColumnType("integer");
        
        List<IntegrationFormFieldVO> convertedFields = Arrays.asList(emptyField);
        List<InParam.ColumnParam> saveParams = Arrays.asList(singleParam);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            try {
                validateParameterTypesMethod.invoke(taskStepService, convertedFields, saveParams, 1L, "测试步骤");
            } catch (Exception e) {
                if (e.getCause() instanceof RuntimeException) {
                    throw (RuntimeException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });
    }

    @Test
    void testValidateParameterTypes_NoMatchingParam_ShouldPass() throws Exception {
        // 准备测试数据 - 没有匹配的参数应该跳过验证
        IntegrationFormFieldVO field = new IntegrationFormFieldVO();
        field.setName("nonExistentField");
        field.setValue("[{\"id\":1}]");
        
        InParam.ColumnParam param = new InParam.ColumnParam();
        param.setColumnName("differentField");
        param.setColumnType("integer");
        
        List<IntegrationFormFieldVO> convertedFields = Arrays.asList(field);
        List<InParam.ColumnParam> saveParams = Arrays.asList(param);
        
        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            try {
                validateParameterTypesMethod.invoke(taskStepService, convertedFields, saveParams, 1L, "测试步骤");
            } catch (Exception e) {
                if (e.getCause() instanceof RuntimeException) {
                    throw (RuntimeException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });
    }
}
