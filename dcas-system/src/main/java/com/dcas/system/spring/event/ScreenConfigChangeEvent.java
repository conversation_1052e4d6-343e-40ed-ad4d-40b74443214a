package com.dcas.system.spring.event;

import com.dcas.common.domain.entity.ScreenConfig;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/10 15:10
 * @since 1.0.0
 */
@Getter
public class ScreenConfigChangeEvent extends ApplicationEvent {
    private static final long serialVersionUID = 4168170048244583552L;
    private final ScreenConfig screenConfig;
    private final ChangeType changeType;

    public ScreenConfigChangeEvent(ScreenConfig screenConfig, ChangeType changeType) {
        super(screenConfig);
        this.screenConfig = screenConfig;
        this.changeType = changeType;
    }

    public enum ChangeType {
        CREATE, UPDATE, DELETE
    }
}
