package com.dcas.system.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.ScreenConfig;
import com.dcas.common.mapper.ScreenConfigMapper;
import com.dcas.system.service.ScreenTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.Trigger;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.PeriodicTrigger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/8 15:15
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScreenTaskScheduler {
    private final ThreadPoolTaskScheduler taskScheduler;
    private final ScreenConfigMapper screenConfigMapper;
    private final ScreenTaskService taskService;

    private final Map<Long, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    @PostConstruct
    public void initTasks() {
        List<ScreenConfig> configs = screenConfigMapper.selectList(new QueryWrapper<ScreenConfig>()
                .eq("data_update_type", 1).eq("del_flag", Boolean.FALSE));

        configs.forEach(this::scheduleTask);
    }

    public void scheduleTask(ScreenConfig config) {
        if (!isValidConfig(config)) {
            cancelTask(config.getId());
            return;
        }

        ScheduledFuture<?> future = taskScheduler.schedule(
                () -> executeTask(config), createTrigger(config)
        );

        scheduledTasks.put(config.getId(), future);
    }

    private boolean isValidConfig(ScreenConfig config) {
        return Objects.nonNull(config)
                && config.getDataUpdateType() == 1
                && Boolean.FALSE.equals(config.getDelFlag());
    }

    private Trigger createTrigger(ScreenConfig config) {
        // 参数有效性验证
        if (config.getMonitorFrequency() <= 0) {
            throw new IllegalArgumentException("监测频率必须大于0");
        }

        // 时间单位转换
        ChronoUnit chronoUnit = ChronoUnit.HOURS;
        long periodMillis = Duration.of(config.getMonitorFrequency(), chronoUnit).toMillis();

        // 创建Trigger并设置参数
        PeriodicTrigger trigger = new PeriodicTrigger(periodMillis);
        trigger.setInitialDelay(0);           // 立即执行第一次
        trigger.setFixedRate(true);           // 固定速率模式
        return trigger;
    }

    private void executeTask(ScreenConfig config) {
        try {
            taskService.executeTask(config);
        } catch (Exception e) {
            log.error("执行定时任务失败，配置ID: {}", config.getId(), e);
        }
    }

    public void cancelTask(Long configId) {
        ScheduledFuture<?> future = scheduledTasks.remove(configId);
        if (Objects.nonNull(future)) {
            future.cancel(false);
        }
    }
}
