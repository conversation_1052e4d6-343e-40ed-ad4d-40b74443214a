package com.dcas.system.report.attachment;

import com.dcas.common.model.dto.ExportWordDto;
import com.dcas.common.model.vo.QueryProjectOperationExportVo;
import com.dcas.system.report.ReportTypeEnum;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 报告接口类
 *
 * <AUTHOR>
 * @date 2024/01/08 15:57
 **/
public interface AttachmentReportInterface {

    /**
     * 导出word报告
     *
     * @param dto
     * @param vo
     * @throws IOException
     */
    String exportWord(ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception;


    /**
     * 导出word报告
     *
     * @param response
     * @param dto
     * @param vo
     * @throws IOException
     */
    void exportWord(HttpServletResponse response, ExportWordDto dto, QueryProjectOperationExportVo vo) throws Exception;

    /**
     * 报告类型
     * @return {@link ReportTypeEnum}
     */
    ReportTypeEnum getType();

}
