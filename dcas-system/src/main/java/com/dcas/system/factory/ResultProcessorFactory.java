package com.dcas.system.factory;

import com.dcas.common.enums.DataSourceType;
import com.dcas.system.handler.ResultProcessor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/18 10:23
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
public class ResultProcessorFactory {
    private final List<ResultProcessor> processors;

    public ResultProcessor getProcessor(DataSourceType dataSourceType) {
        return processors.stream()
                .filter(p -> p.supports(dataSourceType))
                .findFirst()
                .orElse(null);
    }
}
