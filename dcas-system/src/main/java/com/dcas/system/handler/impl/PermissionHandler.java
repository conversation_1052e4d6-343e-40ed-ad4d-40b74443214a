package com.dcas.system.handler.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.impl.LRUCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.dcas.common.annotation.AuthManage;
import com.dcas.common.enums.AuthType;
import com.dcas.common.model.vo.AuthNodeVO;
import com.dcas.system.handler.IPermissionHandler;
import com.dcas.system.service.IMcCenterService;
import com.dcas.system.service.impl.DefaultMcCenterService;
import com.mchz.starter.sso.util.SsoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/4/1 14:52
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionHandler implements IPermissionHandler {

    private final IMcCenterService service;
    private static final Cache<String, List<DefaultMcCenterService.Auth>> CACHE = new LRUCache<>(16, 1800000);

    @Override
    public boolean before(AuthManage annotation) {
        String token;
        try {
            token = SsoUtil.getToken();
        } catch (Exception e) {
            token = "default";
        }
        log.debug("User token:{}", token);
        List<DefaultMcCenterService.Auth> auths = CACHE.get(token);
        if (CollUtil.isEmpty(auths)) {
            auths = service.getAuths();
            CACHE.put(token, auths);
        }
        log.debug(JSONUtil.toJsonStr(auths));
        AuthType[] enumTypes = annotation.auths();
        List<DefaultMcCenterService.Auth> types = Arrays.stream(enumTypes).map(e -> {
            DefaultMcCenterService.Auth auth = new DefaultMcCenterService.Auth();
            auth.setCode(e.getCode());
            auth.setName(e.getName());
            return auth;
        }).collect(Collectors.toList());
        return isContain(auths, types);
    }

    /**
     * 判断types中的元素是否存在于auths中
     *
     * @param types 接口对应的权限
     * @param auths 用户权限
     * @return 是否包含关系
     */
    private boolean isContain(List<DefaultMcCenterService.Auth> types, List<DefaultMcCenterService.Auth> auths) {
        for (DefaultMcCenterService.Auth type : types) {
            if (auths.contains(type)) {
                return true;
            }
        }
        return false;
    }
}
