package com.dcas.system.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.dcas.common.domain.entity.CoProject;
import com.dcas.common.domain.entity.SecurityOperation;
import com.dcas.common.enums.SecurityReportType;
import com.dcas.common.mapper.CoAuthorityMapper;
import com.dcas.common.mapper.CoInventoryMapper;
import com.dcas.common.mapper.CoProjectMapper;
import com.dcas.common.mapper.SecurityProcessLabelMapper;
import com.dcas.common.model.dto.AuthorityOverviewDTO;
import com.dcas.common.model.dto.ExportWordChart;
import com.dcas.common.model.req.SecurityReportCreateReq;
import com.dcas.common.model.vo.SecurityCategoryResultVO;
import com.dcas.common.model.vo.SecurityLabelItemReportVO;
import com.dcas.common.model.vo.SecurityLabelReportVO;
import com.dcas.common.utils.file.FileUtils;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/6/26 16:43
 * @since 1.6.6
 */
@Component
@RequiredArgsConstructor
public class DefaultSecurityReportHandler extends SecurityReportHandler{
    private final CoProjectMapper coProjectMapper;
    private final CoAuthorityMapper coAuthorityMapper;
    private final CoInventoryMapper coInventoryMapper;
    private final SecurityProcessLabelMapper securityProcessLabelMapper;

    @Override
    public SecurityReportType getType() {
        return SecurityReportType.DEFAULT;
    }

    @Override
    @SneakyThrows
    public void export(SecurityReportCreateReq req) {
        SecurityOperation securityOperation = req.getSecurityOperation();
        ClassPathResource classPathResource = new ClassPathResource("template/SecurityOperationTemplate.docx");

        //数据模型
        Map<String, Object> model = new HashMap<>();
        boolean sensitiveFlag = securityOperation.getServiceContent().contains("2");

        String customName = securityOperation.getCustomName();
        if (StrUtil.isBlank(customName)) {
            CoProject coProject = coProjectMapper.selectById(securityOperation.getProjectId());
            customName = coProject.getCustomerName();
        }

        // 基础属性
        model.put("companyName", "美创科技");
        model.put("operationName", securityOperation.getName());
        model.put("createTime", DateUtil.formatChineseDate(securityOperation.getCreateTime(), false, false));
        model.put("finishTime", DateUtil.formatChineseDate(securityOperation.getUpdateTime(), false, false));
        model.put("customName", customName);
        model.put("operationMember", securityOperation.getExecutor());
        int templateScore = Objects.isNull(securityOperation.getTemplateScore()) ? 100 : securityOperation.getTemplateScore();
        model.put("templateScore", templateScore);


        // 图表Map
        Map<String, ExportWordChart> chartMap = req.getChartList().stream().collect(
                Collectors.toMap(ExportWordChart::getName, Function.identity()));

        List<Map<String, Object>> categoryResultList = new ArrayList<>();
        List<Map<String, Object>> categoryContentList = new ArrayList<>();

        // 检查评分规则
        SecurityCategoryResultVO result = req.getResult();
        model.put("rate", NumberUtil.mul(NumberUtil.div(result.getScore(), templateScore, 1), 100) + "%");
        List<String> categoryContent = result.getCategoryResults().stream().map(SecurityCategoryResultVO.CategoryResult::getCategoryName).collect(Collectors.toList());
        List<HashMap<String, Object>> categoryList = result.getCategoryResults().stream().map(r -> new HashMap<String, Object>() {{
            put("category", r.getCategoryName());
            put("categoryScore", r.getCategoryTotalScore());
        }}).collect(Collectors.toList());

        model.put("sensitiveDataCheck", sensitiveFlag);
        if (sensitiveFlag) {
            // 敏感数据检查
            Pair<Long, Long> pair = coInventoryMapper.selectSensitiveCountByOperationId(securityOperation.getId().toString());
            model.put("dataAssetsNum", pair.getKey());
            model.put("highSensitiveAssetsNum", pair.getValue());
            double per = NumberUtil.div(pair.getValue() * 100, (double) pair.getKey(), 1);
            model.put("highSensitiveAssetsProportion", per);
            if (chartMap.containsKey("sensitiveAssetsProportionImg"))
                putPicture(model, chartMap.get("sensitiveAssetsProportionImg"));

            // 高危权限检查
            AuthorityOverviewDTO authorityOverview = coAuthorityMapper.queryStatusQuo(securityOperation.getId().toString());
            int curlUsers = authorityOverview.getCurdPri();
            int dropUsers = authorityOverview.getDropPri();
            int deleteUsers = authorityOverview.getDeletePri();
            int updateUsers = authorityOverview.getUpdatePri();
            int insertUsers = authorityOverview.getInsertPri();
            int selectUsers = authorityOverview.getSelectPri();
            int totalUser = authorityOverview.getTotalNum();
            assert totalUser != 0;
            double curlPer = getPer(curlUsers, totalUser);
            double insertPer = getPer(insertUsers, totalUser);
            double deletePer = getPer(deleteUsers, totalUser);
            double updatePer = getPer(updateUsers, totalUser);
            double selectPer = getPer(selectUsers, totalUser);
            double dropPer = getPer(dropUsers, totalUser);
            model.put("usernameTotal", totalUser);
            model.put("crudTableNum", curlUsers);
            model.put("crudTableNumProportion", curlPer);
            model.put("dropTableNum", dropUsers);
            model.put("dropTableNumProportion", dropPer);
            model.put("deleteTableNum", deleteUsers);
            model.put("deleteTableNumProportion", deletePer);
            model.put("updateTableNum", updateUsers);
            model.put("updateTableNumProportion", updatePer);
            model.put("insertTableNum", insertUsers);
            model.put("insertTableNumProportion", insertPer);
            model.put("selectTableNum", selectUsers);
            model.put("selectTableNumProportion", selectPer);
        }

        // 安全检查结果
        List<SecurityLabelItemReportVO> securityLabelItemReportVOS = securityProcessLabelMapper.selectLabelItemBySecurityId(req.getSecurityOperation().getId());
        Map<SecurityLabelReportVO, List<SecurityLabelItemReportVO>> serityLabelMap =
                securityLabelItemReportVOS.stream()
                        .collect(Collectors.groupingBy(s -> BeanUtil.copyProperties(s, SecurityLabelReportVO.class),
                                TreeMap::new,
                                Collectors.toList()));

        if (CollUtil.isNotEmpty(serityLabelMap)) {
            serityLabelMap.forEach((k, v) -> {
                Map<String, Object> categoryContentMap = new HashMap<>();
                categoryContentMap.put("category", k.getCategory());
                categoryContentMap.put("content", k.getContent());
                categoryContentMap.put("problem", k.getProblem());
                categoryContentMap.put("remark", StrUtil.isBlank(k.getRemark()) ? "无" : k.getRemark());
                List<String> checkList = v.stream().filter(i -> i.getChecked() && !Objects.equals(i.getItemId(), ITEM_ID))
                        .map(SecurityLabelItemReportVO::getItemTitle).collect(Collectors.toList());
                String checkItem = String.join("；", checkList);
                List<String> unCheckList = v.stream().filter(i -> !i.getChecked() && !Objects.equals(i.getItemId(), ITEM_ID))
                        .map(t -> "不符合" + t.getItemTitle()).collect(Collectors.toList());
                String unCheckItem = String.join("；", unCheckList);
                categoryContentMap.put("check", checkItem);
                categoryContentMap.put("uncheck", StrUtil.isEmpty(unCheckItem) ? "无" : unCheckItem);
                categoryContentList.add(categoryContentMap);
            });
        }
        // 安全检查结果总览图
        putPicture(model, chartMap.get("categoryCheckResultImg"));

        List<Map<String, Object>> categoryContentSortList = categoryContentList.stream().sorted(Comparator.comparing(o ->
                categoryContent.indexOf(MapUtil.getStr(o, "category")))).collect(Collectors.toList());

        // 检查结果总结
        result.getCategoryResults().forEach(r -> {
            Map<String, Object> categoryResultMap = new HashMap<>();
            categoryResultMap.put("category", r.getCategoryName());
            categoryResultMap.put("categoryScore", r.getCategoryScore());
            categoryResultMap.put("categoryItemCount", r.getContentResults().size());
            categoryResultMap.put("accordCount", r.getContentResults().stream().filter(c -> Objects.equals(c.getResult(), YES)).count());
            List<SecurityCategoryResultVO.ContentResult> inconsistentResult = r.getContentResults().stream().filter(c -> Objects.equals(c.getResult(), NO)).collect(Collectors.toList());
            categoryResultMap.put("inconsistentCount", inconsistentResult.size());
            categoryResultMap.put("partAccordCount", r.getContentResults().stream().filter(c -> Objects.equals(c.getResult(), PART)).count());
            double present = NumberUtil.mul(NumberUtil.div(r.getCategoryScore(), r.getCategoryTotalScore(), 1), 100).doubleValue();
            categoryResultMap.put("agreement", String.valueOf(present).concat("%"));
            if (CollUtil.isNotEmpty(inconsistentResult)) {
                categoryResultMap.put("unCategoryFlag", Boolean.TRUE);
                categoryResultMap.put("unCategoryContent", inconsistentResult.stream().map(
                        SecurityCategoryResultVO.ContentResult::getContentName).collect(Collectors.joining("、")));
            }
            categoryResultList.add(categoryResultMap);
        });

        model.put("totalCategoryCount", result.getCategoryResults().stream().mapToLong(c -> c.getContentResults().size()).sum());
        model.put("totalAccordCount", result.getCategoryResults().stream().mapToLong(c -> c.getContentResults().stream().filter(cr -> Objects.equals(cr.getResult(), YES)).count()).sum());
        model.put("totalInconsistentCount", result.getCategoryResults().stream().mapToLong(c -> c.getContentResults().stream().filter(cr -> Objects.equals(cr.getResult(), NO)).count()).sum());
        model.put("totalPartAccordCount", result.getCategoryResults().stream().mapToLong(c -> c.getContentResults().stream().filter(cr -> Objects.equals(cr.getResult(), PART)).count()).sum());

        model.put("totalScore", result.getScore());
        model.put("categoryList", categoryList);
        model.put("categoryResultList", categoryResultList);
        model.put("categoryContentList", categoryContentSortList);
        model.put("categoryContent", String.join("、", categoryContent));
        model.put("categoryContentCount", result.getCategoryResults().size());

        // 图表
        putPicture(model, chartMap.get("categoryScoreInfoImg"));
        putPicture(model, chartMap.get("categoryRateInfoImg"));

        try (InputStream inputStream = classPathResource.getInputStream()) {
            LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
            Configure config = Configure.builder().bind("categoryContentList", policy).build();
            String realFileName = String.format("%s报告.docx", securityOperation.getName());

            write(inputStream, req.getResponse(), config, model, realFileName);
        }
    }

    private double getPer(int cur, int total) {
        return total == 0 ? 0 : NumberUtil.div(cur * 100, total, 1);
    }
}
