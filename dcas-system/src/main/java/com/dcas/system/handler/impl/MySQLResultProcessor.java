package com.dcas.system.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.dcas.common.domain.entity.PreSourceConfig;
import com.dcas.common.domain.entity.ScreenHotResources;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.enums.HotResourcesType;
import com.dcas.common.model.dto.DbSqlConfigDTO;
import com.dcas.system.handler.ResultProcessor;
import com.mchz.datasource.cli.RsResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/18 10:26
 * @since 1.0.0
 */
@Slf4j
@Component
public class MySQLResultProcessor implements ResultProcessor {
    @Override
    public boolean supports(DataSourceType dataSourceType) {
        return DataSourceType.MYSQL == dataSourceType || DataSourceType.MYSQL5 == dataSourceType;
    }

    @Override
    public List<ScreenHotResources> process(RsResult result, PreSourceConfig config, DbSqlConfigDTO dbSqlConfig) throws Exception {
        List<ScreenHotResources> list = new ArrayList<>();
        if (StrUtil.equals(dbSqlConfig.getTermName(), "资产访问频率统计")) {
            List<String> sqlList = new ArrayList<>();
            // 用于存储表访问次数
            Map<String, Integer> tableAccessCounts = new HashMap<>();
            // 获取所有活跃的sql语句
            result.getIterator().forEachRemaining(row -> sqlList.add(row[0].toString()));
            for (String tableInfo : sqlList) {
                int fromNum = countKeyword(tableInfo, "from");
                int updateNum = countKeyword(tableInfo, "update");
                int leftNum = countKeyword(tableInfo, "left join");
                int rightNum = countKeyword(tableInfo, "right join");
                int innerNum = countKeyword(tableInfo, "inner join");

                List<String> tableNames = new ArrayList<>(); // 存储提取到的表名

                if (fromNum < 1) {
                    if (updateNum >= 1) {
                        String tableName = extractTableName(tableInfo, "update", "set");
                        tableNames.add(tableName);
                    }
                } else {
                    if (leftNum >= 1) {
                        String tableName = extractTableName(tableInfo, "from", "left join");
                        String tableName1 = extractTableName(tableInfo, "left join", "on");
                        tableNames.add(tableName);
                        tableNames.add(tableName1);
                    } else if (rightNum >= 1) {
                        String tableName = extractTableName(tableInfo, "from", "right join");
                        String tableName1 = extractTableName(tableInfo, "right join", "on");
                        tableNames.add(tableName);
                        tableNames.add(tableName1);
                    } else if (innerNum >= 1) {
                        String tableName = extractTableName(tableInfo, "from", "inner join");
                        String tableName1 = extractTableName(tableInfo, "inner join", "on");
                        tableNames.add(tableName);
                        tableNames.add(tableName1);
                    } else {
                        String tableName = extractTableName(tableInfo, "from", "where");
                        tableNames.add(tableName);
                    }
                }
                tableNames.removeIf(Objects::isNull);
                if (tableNames.isEmpty())
                    continue;
                // 统计表访问次数
                for (String tableName : tableNames) {
                    if (tableName != null && !tableName.isEmpty()) {
                        tableAccessCounts.put(tableName, tableAccessCounts.getOrDefault(tableName, 0) + 1);
                    }
                }

                tableAccessCounts.forEach((k, v) -> {
                    ScreenHotResources screenHotResources = new ScreenHotResources();
                    screenHotResources.setName(k);
                    screenHotResources.setSystemId(config.getSystemId().intValue());
                    screenHotResources.setAccessCount(Long.parseLong(v.toString()));
                    screenHotResources.setTermId(dbSqlConfig.getTermId());
                    screenHotResources.setSourceId(config.getId());
                    screenHotResources.setType(HotResourcesType.ASSET_ACCESS.getCode());
                    list.add(screenHotResources);
                });
            }
        } else if (StrUtil.equals(dbSqlConfig.getTermName(), "账户使用频率统计")) {
            result.getIterator().forEachRemaining(row -> {
                ScreenHotResources screenHotResources = new ScreenHotResources();
                screenHotResources.setName(row[0].toString());
                screenHotResources.setAccessCount(Long.parseLong(row[1].toString()));
                screenHotResources.setSystemId(config.getSystemId().intValue());
                screenHotResources.setTermId(dbSqlConfig.getTermId());
                screenHotResources.setSourceId(config.getId());
                screenHotResources.setType(HotResourcesType.USER_ACCESS.getCode());
                list.add(screenHotResources);
            });
        } else if (StrUtil.equals("僵尸账户统计", dbSqlConfig.getTermName())) {
            result.getIterator().forEachRemaining(row -> {
                ScreenHotResources screenHotResources = new ScreenHotResources();
                screenHotResources.setName(row[0].toString());
                screenHotResources.setAccessCount(0L);
                screenHotResources.setSystemId(config.getSystemId().intValue());
                screenHotResources.setTermId(dbSqlConfig.getTermId());
                screenHotResources.setSourceId(config.getId());
                screenHotResources.setType(HotResourcesType.ZOMBIE_USERS.getCode());
                list.add(screenHotResources);
            });
        }
        return list;
    }

    // 辅助方法：统计关键词出现次数
    private static int countKeyword(String text, String keyword) {
        Pattern pattern = Pattern.compile("\\b" + keyword + "\\b", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }

    // 辅助方法：提取表名
    private static String extractTableName(String text, String startKeyword, String endKeyword) {
        Pattern pattern = Pattern.compile("(?<=" + startKeyword + ")[\\s\\S]*?(?=" + endKeyword + ")", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group().trim();
        }
        return null;
    }
}
