package com.dcas.system.handler;

import com.dcas.common.domain.entity.PreSourceConfig;
import com.dcas.common.domain.entity.ScreenHotResources;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.model.dto.DbSqlConfigDTO;
import com.mchz.datasource.cli.RsResult;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/18 10:24
 * @since 1.0.0
 */
public interface ResultProcessor {
    /**
     * 判断是否支持当前数据源类型
     */
    boolean supports(DataSourceType dataSourceType);

    /**
     * 处理查询结果
     */
    List<ScreenHotResources> process(RsResult result, PreSourceConfig config, DbSqlConfigDTO dbSqlConfig) throws Exception;

}
