package com.dcas.system.handler.impl;

import com.dcas.common.annotation.AuthManage;
import com.dcas.common.exception.PermissionDeniedException;
import com.dcas.system.handler.IPermissionHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/4/1 15:20
 * @since 1.0.0
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class PermissionAdvice {

    private final IPermissionHandler permissionHandler;

    @Pointcut("execution(public * com.dcas..*.*(..))")
    public void permissionCheck() {

    }

    @Around(value = "@annotation(annotation)")
    public Object around(ProceedingJoinPoint point, AuthManage annotation) throws Throwable {
        boolean flag = permissionHandler.before(annotation);
        if (!flag) {
            throw new PermissionDeniedException("请求未授权！");
        }
        return point.proceed();
    }

}
