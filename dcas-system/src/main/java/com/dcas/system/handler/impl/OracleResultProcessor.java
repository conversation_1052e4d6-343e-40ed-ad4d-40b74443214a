package com.dcas.system.handler.impl;

import cn.hutool.core.util.StrUtil;
import com.dcas.common.domain.entity.DbSqlConfig;
import com.dcas.common.domain.entity.PreSourceConfig;
import com.dcas.common.domain.entity.ScreenHotResources;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.enums.HotResourcesType;
import com.dcas.common.model.dto.DbSqlConfigDTO;
import com.dcas.system.handler.ResultProcessor;
import com.mchz.datasource.cli.RsResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/18 14:16
 * @since 1.0.0
 */
@Slf4j
@Component
public class OracleResultProcessor implements ResultProcessor {
    @Override
    public boolean supports(DataSourceType dataSourceType) {
        return DataSourceType.ORACLE == dataSourceType;
    }

    @Override
    public List<ScreenHotResources> process(RsResult result, PreSourceConfig config, DbSqlConfigDTO dbSqlConfig) throws Exception {
        List<ScreenHotResources> list = new ArrayList<>();
        if (StrUtil.equals(dbSqlConfig.getTermName(), "资产访问频率统计")) {
            result.getIterator().forEachRemaining(r -> {
                ScreenHotResources resources = new ScreenHotResources();
                resources.setSystemId(config.getSystemId().intValue());
                resources.setSourceId(config.getId());
                resources.setTermId(dbSqlConfig.getTermId());
                resources.setName(r[0] + "@" + r[1]);
                resources.setAccessCount(Objects.isNull(r[2]) ? 0L : Long.parseLong(r[2].toString()));
                resources.setType(HotResourcesType.ASSET_ACCESS.getCode());
                list.add(resources);
            });
        } else if (StrUtil.equals(dbSqlConfig.getTermName(), "账户使用频率统计")) {
            result.getIterator().forEachRemaining(r -> {
                ScreenHotResources resources = new ScreenHotResources();
                resources.setSystemId(config.getSystemId().intValue());
                resources.setSourceId(config.getId());
                resources.setTermId(dbSqlConfig.getTermId());
                resources.setName(r[0].toString());
                resources.setAccessCount(Objects.isNull(r[1]) ? 0L : Long.parseLong(r[1].toString()));
                resources.setType(HotResourcesType.USER_ACCESS.getCode());
                list.add(resources);
            });
        } else if (StrUtil.equals("僵尸账户统计", dbSqlConfig.getTermName())) {
            result.getIterator().forEachRemaining(row -> {
                ScreenHotResources screenHotResources = new ScreenHotResources();
                screenHotResources.setName(row[0].toString());
                screenHotResources.setAccessCount(0L);
                screenHotResources.setSystemId(config.getSystemId().intValue());
                screenHotResources.setTermId(dbSqlConfig.getTermId());
                screenHotResources.setSourceId(config.getId());
                screenHotResources.setType(HotResourcesType.ZOMBIE_USERS.getCode());
                list.add(screenHotResources);
            });
        }
        return list;
    }
}
