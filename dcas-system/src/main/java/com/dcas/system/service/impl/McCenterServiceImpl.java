//package com.dcas.system.service.impl;
//
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.crypto.digest.DigestUtil;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import cn.hutool.http.HttpStatus;
//import cn.hutool.http.Method;
//import cn.hutool.json.JSONUtil;
//import com.google.common.collect.Lists;
////import com.dcas.common.config.McCenterConfig;
////import com.dcas.common.config.UserCenter;
//import com.dcas.common.exception.ServiceException;
////import com.dcas.starter.sso.util.SsoUtil;
//import com.dcas.common.model.dto.SSOAccountDTO;
//import com.dcas.common.model.dto.SSOAccountListDTO;
////import com.dcas.system.service.McCenterService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <p></p>
// *
// * <AUTHOR>
// * @date 2022/11/8 10:30
// * @since 1.7.0
// */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class McCenterServiceImpl implements McCenterService {
//
//    private final McCenterConfig mcCenterConfig;
//
//    @Override
//    public List<SSOAccountDTO> getSsoUsers() {
//        if (!mcCenterConfig.getEnabled())
//            return Lists.newArrayList();
//        UserCenter userCenter = mcCenterConfig.getUserCenter();
//        HttpRequest request = HttpRequest.of(userCenter.getHost() + "account?page=1&size=65535").method(Method.GET);
//        String response = this.doRequest(userCenter, request, null);
//        return JSONUtil.parseObj(response).toBean(SSOAccountListDTO.class).getList();
//    }
//
//    private String doRequest(UserCenter userCenter, HttpRequest httpRequest, Object body) {
//        httpRequest.header("AppId", userCenter.getAppId()).header("AppSecret", DigestUtil.sha256Hex(userCenter.getAppId() + userCenter.getAppSecret())).timeout(30_000);
//        String token = getToken();
//        if (StrUtil.isNotBlank(token)) {
//            httpRequest.header("Authorization", token);
//        }
//
//        String payload = "";
//        if (body != null) {
//            payload = JSONUtil.toJsonStr(body);
//            httpRequest.body(payload);
//        }
//
//        log.debug("[统一登录平台] >> url:{}, body:{}", httpRequest.getUrl(), payload);
//        HttpResponse response = httpRequest.execute();
//        log.debug("[统一登录平台] << {}", response != null ? response.body() : "");
//        if (response != null) {
//            if (response.getStatus() == 404) {
//                throw new ServiceException(HttpStatus.HTTP_NOT_FOUND, response.body());
//            }
//
//            if (!response.isOk()) {
//                throw new ServiceException(response.body());
//            }
//        }
//
//        return response != null ? response.body() : "";
//    }
//
//    private String getToken() {
//        return SsoUtil.getToken();
//    }
//}
