package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.ScreenConfig;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.CoOperationMapper;
import com.dcas.common.mapper.ScreenConfigMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.model.dto.QueryOperationDTO;
import com.dcas.common.model.dto.ScreenConfigDTO;
import com.dcas.common.model.vo.ScreenConfigVO;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.system.service.IScreenConfigService;
import com.dcas.system.spring.event.ScreenConfigChangeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static com.dcas.common.enums.CommonResultCode.MC_SCREEN_VALID_ERROR;

/**
 * <p>
 * 大屏配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ScreenConfigServiceImpl extends ServiceImpl<ScreenConfigMapper, ScreenConfig> implements
    IScreenConfigService {

    private final ScreenConfigMapper screenConfigMapper;
    private final ApplicationEventPublisher eventPublisher;
    private final CoOperationMapper coOperationMapper;

    @Override
    public List<ScreenConfigVO> listAll() {
        List<ScreenConfig> screenConfigList = screenConfigMapper.selectList(new QueryWrapper<ScreenConfig>().eq("del_flag", false));
        List<ScreenConfigVO> list = new ArrayList<>();
        if (CollUtil.isEmpty(screenConfigList)){
            return list;
        }
        screenConfigList.forEach(screenConfig -> {
            ScreenConfigVO screenConfigVO = new ScreenConfigVO();
            BeanUtils.copyProperties(screenConfig, screenConfigVO);
            list.add(screenConfigVO);
        });
        return list;
    }

    @Override
    public void saveScreenConfig(ScreenConfigDTO screenConfigDTO) {
        // 校验业务系统最小要求
        validateBusSystem(screenConfigDTO.getBusSystemNames());
        // 校验大屏模板
        validateScreenTemplate(screenConfigDTO.getTemplate());
        ScreenConfig screenConfig = new ScreenConfig();
        BeanUtils.copyProperties(screenConfigDTO, screenConfig);
        screenConfig.setDelFlag(false);
        screenConfig.setCreateTime(LocalDateTime.now());
        screenConfig.setUpdateTime(LocalDateTime.now());
        screenConfig.setCreateBy(SecurityUtils.getAccount());
        screenConfig.setUpdateBy(SecurityUtils.getAccount());
        screenConfigMapper.insert(screenConfig);
        publishEvent(screenConfig, ScreenConfigChangeEvent.ChangeType.CREATE);
    }

    private void validateScreenTemplate(String template) {
        if (screenConfigMapper.selectCount(new QueryWrapper<ScreenConfig>().eq("template", template)) > 1){
            throw new ServiceException("大屏模板已被使用",CommonResultCode.FAILURE.getCode());
        }
    }

    private void validateBusSystem(String[] busSystemNames) {
        for (String busSystem : busSystemNames){
            List<String> list = coOperationMapper.selectOperationServiceContent(busSystem);
            String message = busSystem+"系统缺少大屏初始化数据，请先在评估作业中创建包含所有评估模块和能力模块的作业，并完成结项后，再返回添加业务系统。";
            if (CollUtil.isEmpty(list)){
                throw new ServiceException(message,MC_SCREEN_VALID_ERROR.getCode());
            }

            // 多作业求并集
            Set<Long> allList = new HashSet<>();
            list.forEach(s -> {
                //评估内容
                List<Long> serviceContentList = JSON.parseArray("[" + s + "]", Long.class);
                allList.addAll(serviceContentList);
            });
            if (!allList.contains(LabelEnum.SMZQ.getCode())
                || !allList.contains(LabelEnum.ZZDY.getCode())
                || !allList.contains(LabelEnum.ZCPD.getCode())
                || !allList.contains(LabelEnum.JCHJ.getCode())
                || !allList.contains(LabelEnum.SJQX.getCode())
                || !allList.contains(LabelEnum.NLFX.getCode())
                || !allList.contains(LabelEnum.XZHY.getCode())
                || !allList.contains(LabelEnum.HFHG.getCode())
                || !allList.contains(LabelEnum.CJFX.getCode())) {
                log.info("Service content list : {} , need service content : {},{},{},{},{},{},{},{},{}",
                    allList, LabelEnum.SMZQ.getCode(), LabelEnum.ZZDY.getCode(),
                    LabelEnum.ZCPD.getCode(), LabelEnum.JCHJ.getCode(), LabelEnum.SJQX.getCode(),
                    LabelEnum.NLFX.getCode(), LabelEnum.XZHY.getCode(), LabelEnum.HFHG.getCode(),
                    LabelEnum.CJFX.getCode());
                throw new ServiceException(message,MC_SCREEN_VALID_ERROR.getCode());
            }
        }
    }

    @Override
    public void updateScreenConfig(ScreenConfigDTO screenConfigDTO) {
        // 校验业务系统最小要求
        validateBusSystem(screenConfigDTO.getBusSystemNames());
        ScreenConfig screenConfig = new ScreenConfig();
        BeanUtils.copyProperties(screenConfigDTO, screenConfig);
        screenConfig.setDelFlag(false);
        screenConfig.setUpdateTime(LocalDateTime.now());
        screenConfig.setUpdateBy(SecurityUtils.getAccount());
        screenConfigMapper.updateById(screenConfig);
        publishEvent(screenConfig, ScreenConfigChangeEvent.ChangeType.UPDATE);
    }

    @Override
    public void publishEvent(ScreenConfig config, ScreenConfigChangeEvent.ChangeType type) {
        try {
            eventPublisher.publishEvent(new ScreenConfigChangeEvent(config, type));
        } catch (Exception e) {
            // 事件发布失败不影响主流程
            log.error("发布配置变更事件失败", e);
        }
    }
}
