package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.core.param.DataSourceParam;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.DbSqlConfigMapper;
import com.dcas.common.mapper.PreSourceConfigMapper;
import com.dcas.common.model.dto.DbSqlConfigDTO;
import com.dcas.common.utils.PartitionUtils;
import com.dcas.system.factory.ResultProcessorFactory;
import com.dcas.system.handler.ResultProcessor;
import com.dcas.system.service.DatabaseService;
import com.dcas.system.service.ScreenTaskService;
import com.mchz.datasource.cli.RsResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/7 14:52
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScreenTaskServiceImpl implements ScreenTaskService {
    private final DatabaseService databaseService;
    private final DbSqlConfigMapper sqlConfigMapper;
    private final ScreenHotResourcesServiceImpl screenHotResourcesService;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final ResultProcessorFactory resultProcessorFactory;

    @Override
    @SchemaSwitch
    @Transactional(rollbackFor = Exception.class)
    public void executeTask(ScreenConfig config) {
        if (!isValidConfig(config)) {
            return;
        }

        try {
            // 获取热力图SQL配置 key数据源类型 value热点资源查询配置
            Map<Integer, List<DbSqlConfigDTO>> dbSqlGroup = sqlConfigMapper.queryByType(1)
                    .stream().collect(Collectors.groupingBy(DbSqlConfig::getDbType));

            List<Long> systemIds = Arrays.stream(config.getBusSystemIds()).mapToLong(i -> (long) i).boxed().collect(Collectors.toList());
            List<PreSourceConfig> preSourceConfigs = preSourceConfigMapper.selectList(
                    new QueryWrapper<PreSourceConfig>().eq("operation_id", "-1").in("system_id", systemIds))
                    // 过滤出有配置对应数据库类型的查询SQL
                    .stream().filter(p -> dbSqlGroup.containsKey(p.getConfigType())).collect(Collectors.toList());
            if (CollUtil.isEmpty(preSourceConfigs)) {
                log.warn("无可处理数据源配置，大屏配置ID: {}", config.getId());
                return;
            }
            // 删除旧数据
            screenHotResourcesService.remove(new QueryWrapper<ScreenHotResources>().in("system_id", systemIds));

            // 执行每个业务系统的查询
            for (PreSourceConfig sourceConfig : preSourceConfigs) {
                processSystemQueries(sourceConfig, dbSqlGroup.get(sourceConfig.getConfigType()));
            }
        } catch (Exception e) {
            log.error("查询热点资源任务失败，大屏配置ID: {}", config.getId(), e);
            throw new ServiceException("任务执行失败");
        }
    }

    private boolean isValidConfig(ScreenConfig config) {
        return Objects.nonNull(config)
                && config.getDataUpdateType() == 1
                && Boolean.FALSE.equals(config.getDelFlag())
                && ArrayUtils.isNotEmpty(config.getBusSystemIds());
    }

    private void processSystemQueries(PreSourceConfig sourceConfig, List<DbSqlConfigDTO> dbSqlConfigList) throws Exception {
        PreSourceConfig preSourceConfig = preSourceConfigMapper.selectById(sourceConfig.getId());
        if (Objects.isNull(preSourceConfig)) {
            log.warn("数据源配置不存在, ID: {}", sourceConfig.getId());
            return;
        }
        // 获取处理器
        DataSourceType type = DataSourceType.getType(preSourceConfig.getConfigType());
        ResultProcessor processor = resultProcessorFactory.getProcessor(type);
        if (Objects.isNull(processor)) {
            log.error("未找到对应的处理器，数据源类型: {}", type.getName());
            return;
        }
        DataSourceParam param = databaseService.toSourceConfigUsable(preSourceConfig);

        for (DbSqlConfigDTO dto : dbSqlConfigList) {
            RsResult result = databaseService.openQuery(param, dto.getSql());
            // 根据不同的数据源类型，有不同的方式处理result
            List<ScreenHotResources> resources = processor.process(result, preSourceConfig, dto);
            PartitionUtils.part(resources, screenHotResourcesService::saveBatch);
        }
    }
}
