package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dcas.common.domain.entity.*;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.enums.LabelEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.*;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.req.SourceRangeConfirmReq;
import com.dcas.common.model.vo.SystemDetailVO;
import com.dcas.common.model.vo.SystemSourceVO;
import com.dcas.common.utils.PageResult;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.system.service.CoSystemService;
import com.dcas.system.service.IScreenConfigService;
import com.dcas.system.spring.event.ScreenConfigChangeEvent;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/8 17:09
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class CoSystemServiceImpl implements CoSystemService {

    private final CoSystemMapper coSystemMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final ScreenDatasourceMapper screenDatasourceMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final IScreenConfigService iScreenConfigService;




    @Override
    public void add(SystemAddDTO dto) {
        if (hasSameName(dto.getName())) {
            throw new ServiceException("存在同名业务系统，请重新输入");
        }
        CoSystem coSystem = new CoSystem();
        coSystem.setName(dto.getName());
        coSystem.setIntroduce(dto.getIntroduce());
        coSystemMapper.insert(coSystem);
    }

    @Override
    public void update(SystemEditDTO dto) {
        if (hasSameName(dto.getName())) {
            throw new ServiceException("存在同名业务系统，请重新输入");
        }
        CoSystem coSystem = new CoSystem();
        coSystem.setId(dto.getId());
        coSystem.setName(dto.getName());
        coSystem.setIntroduce(dto.getIntroduce());
        coSystemMapper.updateById(coSystem);
    }

    @Override
    public PageResult<SystemDetailVO> pageQuery(SystemQueryDTO dto) {
        try (Page<?> page = PageHelper.startPage(dto.getCurrentPage(), dto.getPageSize())) {
            QueryWrapper<CoSystem> queryWrapper = new QueryWrapper<>();
            if (StrUtil.isNotEmpty(dto.getOperationId())) {
                // 筛选作业绑定业务系统数据源
                List<Long> systemIds = dynamicProcessTreeMapper.selectTreeIdBySrc(dto.getOperationId(), LabelEnum.XTDY.getCode());
                if (CollUtil.isNotEmpty(systemIds)) {
                    queryWrapper.in("id", systemIds);
                } else {
                    return PageResult.ofEmpty();
                }
            }
            if (StrUtil.isNotEmpty(dto.getName())) {
                queryWrapper.like("name", dto.getName());
            }
            if (StrUtil.isNotEmpty(dto.getIntroduce())) {
                queryWrapper.like("introduce", dto.getIntroduce());
            }
            List<CoSystem> list = coSystemMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(list)) {
                List<Integer> systemIds = list.stream().map(CoSystem::getId).collect(Collectors.toList());
                Map<Long, List<PreSourceConfig>> systemSourceGroup = preSourceConfigMapper.selectList(
                        new QueryWrapper<PreSourceConfig>().eq("operation_id", "-1")
                                .in("system_id", systemIds)).stream().collect(Collectors.groupingBy(PreSourceConfig::getSystemId));
                // 当为大屏配置调用时，需为大屏数源配置的业务系统
                Set<Integer> systemIdSet = new HashSet<>();
                if (dto.getType() != null && dto.getType() == 2){
                    List<ScreenDatasource> screenDatasources = screenDatasourceMapper.selectList(null);
                    if (CollUtil.isNotEmpty(screenDatasources)){
                        systemIdSet = screenDatasources.stream().map(ScreenDatasource::getSystemId).collect(Collectors.toSet());
                        Set<Integer> finalSystemIdSet1 = systemIdSet;
                        list = list.stream().filter(s-> finalSystemIdSet1.contains(s.getId())).collect(Collectors.toList());
                    }
                }
                List<SystemDetailVO> res = list.stream().map(l -> {
                    SystemDetailVO systemDetailVO = new SystemDetailVO();
                    systemDetailVO.setId(l.getId());
                    systemDetailVO.setName(l.getName());
                    systemDetailVO.setIntroduce(l.getIntroduce());
                    List<PreSourceConfig> preSourceConfigs = systemSourceGroup.get(l.getId().longValue());
                    if (CollUtil.isNotEmpty(preSourceConfigs)) {
                        systemDetailVO.setSourceList(preSourceConfigs.stream().map(config -> {
                            SystemSourceVO vo = new SystemSourceVO();
                            vo.setConfigId(config.getId());
                            vo.setName(config.getConfigName());
                            String detail = config.getDbConfig(Boolean.TRUE);
                            detail = detail + StrUtil.format("数据库类型:【{}】", DataSourceType.getType(config.getConfigType()).getName());
                            vo.setDetail(detail);
                            return vo;
                        }).collect(Collectors.toList()));
                        // 作业查询时过滤未绑定数据源的业务系统
                    } else if (StrUtil.isNotEmpty(dto.getOperationId())) {
                        return null;
                    }
                    return systemDetailVO;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                return PageResult.ofPage(page.getTotal(), res);
            }
            return PageResult.ofEmpty();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(IdsReq ids) {
        // 校验业务系统是否存在于配置的检测大屏任务中和作业引用，存在则不允许删除
        ids.setType(1);
        validateIsUsed(ids);
        coSystemMapper.deleteBatchIds(ids.getIds());
        // 删除全局数据源中关联了该业务系统的数据源配置
        List<PreSourceConfig> preSourceConfigs = preSourceConfigMapper.selectList(
                new QueryWrapper<PreSourceConfig>().in("system_id", ids.getIds()).eq("operation_id", "-1"));
        List<Integer> configIds = preSourceConfigs.stream().map(PreSourceConfig::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(configIds)) {
            UpdateWrapper<PreSourceConfig> updateWrapper = new UpdateWrapper<>();
            updateWrapper.set("system_id", null).in("id", configIds);
            preSourceConfigMapper.update(null, updateWrapper);
        }
    }

    private void validateIsUsed(IdsReq ids) {
        switch (ids.getType()){
            case 1:
                ids.getIds().forEach(id -> {
                    Integer count = screenDatasourceMapper.selectCount(new QueryWrapper<ScreenDatasource>().eq("system_id", id));
                    Integer countA = dynamicProcessTreeMapper.selectCount(new QueryWrapper<DynamicProcessTree>().eq("tree_id", id));
                    if (countA > 0){
                        throw new ServiceException("该业务系统正在被作业调用，无法删除！");
                    }
                    if (count > 0){
                        throw new ServiceException("该业务系统正在被数据大屏调用，无法删除！");
                    }
                });
                break;
            case 2:
                ids.getIds().forEach(id -> {
                    Integer count = screenDatasourceMapper.selectCount(new QueryWrapper<ScreenDatasource>().eq("source_id", id));
                    Integer countA = preSourceConfigMapper.selectCount(new QueryWrapper<PreSourceConfig>().ne("operation_id", "-1").eq("id", id));
                    if (countA > 0){
                        throw new ServiceException("该数源正在被作业调用，无法删除！");
                    }
                    if (count > 0){
                        throw new ServiceException("该数源正在被数据大屏调用，无法删除！");
                    }
                });
                break;
            default:
                break;
        }

    }

    @Override
    public void addSource(SystemSourceAdd req) {
        PreSourceConfig config = preSourceConfigMapper.selectById(req.getConfigId());
        if (Objects.isNull(config))
            throw new ServiceException("数据源配置不存在");
        PreSourceConfig sourceConfig = new PreSourceConfig();
        sourceConfig.setId(req.getConfigId());
        sourceConfig.setSystemId(req.getId());
        sourceConfig.setDbName(config.getDbName());
        List<String> schemas = req.getSchemas();
        if (CollUtil.isNotEmpty(schemas)) {
            String schemaJson = JSONUtil.toJsonStr(schemas);
            sourceConfig.setSchemas(schemaJson);
        }
        preSourceConfigMapper.updateById(sourceConfig);

        // 同步到大屏数源 20250321
        if (screenDatasourceMapper.selectCount(new QueryWrapper<ScreenDatasource>().eq("system_id", req.getId()).eq("source_id", req.getConfigId())) == 0){
            ScreenDatasource screenDatasource = new ScreenDatasource();
            screenDatasource.setSourceId(req.getConfigId());
            screenDatasource.setSystemId(Math.toIntExact(req.getId()));
            screenDatasource.setCreateTime(LocalDateTime.now());
            screenDatasource.setCreateBy("dcas");
            screenDatasource.setUpdateTime(LocalDateTime.now());
            screenDatasource.setUpdateBy("dcas");
            screenDatasource.setDelFlag(false);
            screenDatasource.setStatus(1);
            screenDatasourceMapper.insert(screenDatasource);

            // 先判断大屏配置是否引用该业务系统，若引用，则通知事件，做大屏更新处理
            List<ScreenConfig> screenConfigList = iScreenConfigService.list();
            screenConfigList.forEach(screenConfig -> {
                if (Arrays.asList(screenConfig.getBusSystemIds()).contains(Math.toIntExact(req.getId()))) {
                    iScreenConfigService.publishEvent(screenConfig, ScreenConfigChangeEvent.ChangeType.UPDATE);
                }
            });

        }

    }

    @Override
    public void deleteSource(SystemSourceAdd req) {
        PreSourceConfig config = preSourceConfigMapper.selectById(req.getConfigId());
        if (Objects.isNull(config))
            throw new ServiceException("数据源配置不存在");
        IdsReq idsReq = new IdsReq();
        idsReq.setType(2);
        idsReq.setIds(Collections.singletonList(req.getConfigId()));
        validateIsUsed(idsReq);
        PreSourceConfig sourceConfig = new PreSourceConfig();
        sourceConfig.setId(req.getConfigId());
        sourceConfig.setSystemId(null);
        sourceConfig.setDbName(config.getDbName());
        preSourceConfigMapper.updateById(sourceConfig);
    }

    @Override
    public List<SystemSourceVO> getSource(Integer systemId) {
        List<PreSourceConfig> preSourceConfigs = preSourceConfigMapper.selectList(
                new QueryWrapper<PreSourceConfig>()
                        .eq("operation_id", "-1")
                        .and(wrapper -> wrapper.isNull("system_id").or().eq("system_id", systemId))
        );
        return preSourceConfigs.stream().map(config -> {
            SystemSourceVO vo = new SystemSourceVO();
            vo.setConfigId(config.getId());
            vo.setName(config.getConfigName());
            return vo;
        }).collect(Collectors.toList());
    }

    // 判断是否有同名业务系统
    private boolean hasSameName(String name) {
        Integer count = coSystemMapper.selectCount(new QueryWrapper<CoSystem>().eq("name", name));
        return count > 0;
    }

    @Override
    public Pair<Integer, String> listKeyValue(String operationId) {
        // 筛选作业绑定业务系统数据源
        return dynamicProcessTreeMapper.selectKeyValueByOperationId(operationId, LabelEnum.XTDY.getCode());

    }

    @Override
    public List<TreeLabelDTO> listByOperationId(String operationId) {
        return dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode());
    }
}
