package com.dcas.system.service;

import com.dcas.common.domain.entity.ScreenConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.model.dto.ScreenConfigDTO;
import com.dcas.common.model.vo.ScreenConfigVO;
import com.dcas.system.spring.event.ScreenConfigChangeEvent;

import java.util.List;

/**
 * <p>
 * 大屏配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IScreenConfigService extends IService<ScreenConfig> {

    List<ScreenConfigVO> listAll();

    void saveScreenConfig(ScreenConfigDTO screenConfigDTO);

    void updateScreenConfig(ScreenConfigDTO screenConfigDTO);

    void publishEvent(ScreenConfig config, ScreenConfigChangeEvent.ChangeType type);
}
