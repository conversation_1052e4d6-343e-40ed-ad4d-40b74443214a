package com.dcas.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.algorithms.SnowFlake;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.constant.Constants;
import com.dcas.common.enums.*;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.FileAnalysisLogicDTO;
import com.dcas.system.calc.spec.BetweenBaseRule;
import com.dcas.common.model.dto.SpecCalcSystemResultDTO;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.domain.entity.*;
import com.dcas.common.model.param.AnalysisParam;
import com.dcas.common.model.req.AnalysisCharReq;
import com.dcas.common.model.vo.*;
import com.dcas.common.mapper.*;
import com.dcas.system.predicate.MyPredicate;
import com.dcas.system.service.IRiskAnalysisService;
import com.dcas.system.service.ModelAnalysisService;
import com.dcas.system.service.SpecCalcResultService;
import com.mchz.dcas.client.enums.ConditionType;
import com.mchz.dcas.client.enums.FileAnalysisType;
import com.mchz.dcas.client.enums.OperateType;
import com.mchz.dcas.client.model.analysis.MiddleDTO;
import com.mchz.dcas.client.model.dto.IntervalDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/13 9:40
 * @since 1.2.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ModelAnalysisServiceImpl implements ModelAnalysisService {
    private static final String RESULT = "符合性分析";
    private static final String RISK = "风险等级";
    private static final String RATE = "符合率";
    private static final String TECH_ABILITY = "技术";

    private final CoInventoryMapper coInventoryMapper;
    private final CoOperationMapper coOperationMapper;
    private final CoConstantMapper coConstantMapper;
    private final CoVerificationMapper coVerificationMapper;
    private final CoThreatAnalysisMapper coThreatAnalysisMapper;
    private final AnalysisTemplateMapper analysisTemplateMapper;
    private final DynamicProcessTreeMapper dynamicProcessTreeMapper;
    private final LawTemplateRelevanceMapper lawTemplateRelevanceMapper;
    private final QuestionnaireContentMapper questionnaireContentMapper;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final SpecialEvaluationConditionConfigMapper specialEvaluationConditionConfigMapper;
    private final SpecialEvaluationConfigMapper specialEvaluationConfigMapper;
    private final FileAnalysisLogicMapper fileAnalysisLogicMapper;
    private final SpecCalcResultService specCalcResultService;
    private final StandardMapper standardMapper;
    private final TagMapper tagMapper;
    private final QuestionnaireItemDegreeServiceImpl questionnaireItemDegreeServiceImpl;

    private static final Map<String, Function<AnalysisStandardVO, String>> DIMENSION_MAPPINGS =
            new HashMap<String, Function<AnalysisStandardVO, String>>() {
                private static final long serialVersionUID = -5493739879162704282L;
                {
                put("stage", AnalysisStandardVO::getStage);
                put("process", AnalysisStandardVO::getProcess);
                put("dimension", AnalysisStandardVO::getDimension);
                put("classify", AnalysisStandardVO::getClassify);
                put("level", AnalysisStandardVO::getLevel);
            }};


    @Override
    @SchemaSwitch(value = AnalysisParam.class)
    public List<CoLegal> complianceAnalysis(AnalysisParam param) {
        final Map<Long, String> systemMap = getBusSystemMap(param.getOperationId());
        List<CoLegal> coLegalList = new ArrayList<>();

        // 判断是否需要判分
        boolean needCalc;
        // 重要数据或者核心数据资产数
        long dataTagCount = 0L;
        if (param.getIsSpec() != null && param.getIsSpec()) {
            SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(param.getSpecId());
            needCalc = "LAW".equals(specialEvaluationConfig.getCalcTemplate());
            // 特殊处理：目前电信专项，电信专项的合规评估，
            //电信专项的合规评估，需要区分一般数据、重要数据和核心数据，COC已经已经打过标签，资产里面如果没有核心数据和重要数据，合规评估里面专门针对的条文就是不适用的，无需评估；资产里面有核心数据或者重要数据，那么合规评估里面就全部都评估
            List<CoInventory> coInventories = coInventoryMapper.selectList(new QueryWrapper<CoInventory>().eq("operation_id", param.getOperationId()));
            dataTagCount = coInventories.stream().filter(coInventory -> !DataTagEnum.GENERAL.getTag().equals(coInventory.getDataTag())).count();
        } else {
            needCalc = false;
        }

        // 获取一般数据标签ID
        String generalTag = null;
        if (dataTagCount == 0 && needCalc){
            generalTag = tagMapper.selectTagIdByName(DataTagEnum.GENERAL.getDesc());
        }
        // key:法律条文 value:法律条文关联核查项id列表
        final Map<ComplianceLawVO, List<ComplianceLawItemVO>> complianceLawMap = lawTemplateRelevanceMapper.qryByTemplateId(
            param.getTemplateId(), generalTag).stream().collect(Collectors.groupingBy(l -> BeanUtil.copyProperties(l, ComplianceLawVO.class)));
        // 查询可用调研问卷，排除勾选'以上选项均不符合'问题下的核查项后 已勾选的核查项
        final Map<Long, Map<String, List<QuestionnaireContent>>> contentMap = getContentMap(param.getOperationId(), systemMap);

        // 判断作业是否存在现状核验
        boolean containsVerification = checkContainsLabel(param.getOperationId(), LabelEnum.XZHY.getCode());

        final Map<Integer, FileAnalysisLogic> analysisMap = fileAnalysisLogicMapper.selectList(
                new QueryWrapper<FileAnalysisLogic>().eq("file_type", "LAW")).stream().collect(Collectors.toMap(FileAnalysisLogic::getFileId, Function.identity()));
        final Map<String, Integer> itemDegreeMap = questionnaireItemDegreeServiceImpl.list(new QueryWrapper<QuestionnaireItemDegree>()
                .eq("operation_id", param.getOperationId())).stream().collect(Collectors.toMap(QuestionnaireItemDegree::getItemId, QuestionnaireItemDegree::getDegree, Math::max));
        final Map<Integer, FileAnalysisLogicDTO> analysisConditionMap = new HashMap<>();

        // 获取专项评估判断条件
        List<SpecialEvaluationConditionConfig> conditionConfigs = specialEvaluationConditionConfigMapper.selectList(
            new QueryWrapper<SpecialEvaluationConditionConfig>().eq("evaluation_id", param.getSpecId()).eq("type", 1));
        // 是否有大部分符合
        boolean exist = conditionConfigs.stream()
            .anyMatch(specialEvaluationConditionConfig -> "0.7".equals(specialEvaluationConditionConfig.getResult()));
        List<SpecCalcSystemResultDTO> specCalcSystemResults = new ArrayList<>();
        complianceLawMap.forEach((key, value1) -> {
            List<ComplianceLawItemVO> value = value1.stream().filter(v -> Objects.nonNull(v.getItemId())).collect(Collectors.toList());
            // 是否需要进行例外分析
            final FileAnalysisLogic fileAnalysisLogic = analysisMap.get(key.getLawId());
            analysisConditionMap.putIfAbsent(key.getLawId(), analysisCondition(fileAnalysisLogic));
            FileAnalysisLogicDTO analysisLogicDTO = analysisConditionMap.get(key.getLawId());
            contentMap.forEach((systemId, questionnaires) -> {
                double total = value.size();
                double count = 0.0;
                // 是否存在不符合的核查项，whether条件专用
                boolean hasInCompliant = false;
                // 是否为whether例外情况,whether只要有不符合的就不符合
                boolean whetherException = Objects.nonNull(analysisLogicDTO) && Objects.equals(analysisLogicDTO.getAnalysisType(), "WHETHER");
                StringBuilder explain = new StringBuilder();
                StringBuilder nonMatchExplain = new StringBuilder();
                Set<String> duplicateSet = new HashSet<>();
                Set<String> nonMatchDuplicateSet = new HashSet<>();
                Set<String> nonMatchRiskDescDuplicateSet = new HashSet<>();
                for (ComplianceLawItemVO item : value) {
                    List<QuestionnaireContent> contentList = questionnaires.get(item.getItemId());
                    QuestionnaireContent content = filterQuestionnaireContent(contentList, key.getConditionTags(), key.getHasException(), analysisLogicDTO);
                    if (Objects.nonNull(content)) {
                        // 不适用，排除分母中的数量
                        if (Objects.nonNull(content.getInapplicable()) && content.getInapplicable()) {
                            total--;
                            continue;
                        }
                        // 不为反例，符合数加一
                        if (content.getChecked() && (Objects.isNull(content.getReverse()) || !content.getReverse())) {
                            double itemScore = calculateCount(analysisLogicDTO, key.getHasException(), item.getItemId(), itemDegreeMap, key.getConditionTags(), content.getConditionTags());
                            if (whetherException && !hasInCompliant) {
                                // 判断whether例外情况中是否有不符合的检查项，只需要判断一次
                                hasInCompliant = itemScore == 0;
                            }
                            count += itemScore;
                            if (containsVerification) {
                                if (StrUtil.isNotEmpty(content.getExplain()) && !duplicateSet.contains(content.getExplain())) {
                                    explain.append(content.getExplain()).append(";");
                                    duplicateSet.add(content.getExplain());
                                }
                            } else {
                                if (!duplicateSet.contains(content.getItemTitle())) {
                                    explain.append(content.getItemTitle()).append(";");
                                    duplicateSet.add(content.getItemTitle());
                                }
                            }
                        } else {
                            // 不符合
                            // 若包含现状核验，则取现状核验中的描述说明
                            if (containsVerification) {
                                if (StrUtil.isNotEmpty(content.getExplain()) && !nonMatchDuplicateSet.contains(content.getExplain())) {
                                    nonMatchExplain.append(content.getExplain()).append(";");
                                    nonMatchDuplicateSet.add(content.getExplain());
                                    nonMatchRiskDescDuplicateSet.add(item.getRiskDesc());
                                }
                            } else {
                                if (StrUtil.isNotEmpty(item.getDescribe()) && !nonMatchDuplicateSet.contains(item.getDescribe())) {
                                    nonMatchExplain.append(item.getDescribe()).append(";");
                                    nonMatchDuplicateSet.add(item.getDescribe());
                                    nonMatchRiskDescDuplicateSet.add(item.getRiskDesc());
                                }
                            }
                            // 如果是whether例外情况，且调研问卷没有勾选该核查项
                            if (whetherException)
                                hasInCompliant = true;
                        }
                    } else {
                        total--;
                    }
                }

                // 判定符合性
                Map<String, String> judgeMap;
                BigDecimal score;
                if (needCalc){
                    // 专项评估增加大部分符合判定
                    judgeMap = judgeForSpec(count, total, exist);
                    String res = calcSpecScore(count, total, conditionConfigs);
                    score = StrUtil.isEmpty(res) ? null : BigDecimal.valueOf(Double.parseDouble(res));
                    log.info("count:{},total:{},score:{}",count,total,score);
                    SpecCalcSystemResultDTO specCalcSystemResultDTO =
                        SpecCalcSystemResultDTO.builder()
                            .bpCode(key.getLawItemId())
                            .operationId(param.getOperationId())
                            .type("LAW")
                            .score(score)
                            .stage(key.getDocCode())
                            .process(key.getName())
                            .level(key.getLawId())
                            .systemId(String.valueOf(systemId))
                            .standardId(key.getLawId())
                            .build();
                    specCalcSystemResults.add(specCalcSystemResultDTO);
                } else if (whetherException) {
                    judgeMap = judgeException(count, total, fileAnalysisLogic, analysisLogicDTO, hasInCompliant);
                    score = scoreJudgeScore(count, total, fileAnalysisLogic, hasInCompliant);
                } else {
                    judgeMap = judge(count, total);
                    score = judgeScore(count, total);
                }
                // 业务系统维度，一个条文对应多系统
                CoLegal coLegal = CoLegal.builder()
                        .operationId(param.getOperationId())
                        .lawId(key.getLawId())
                        .lawName(key.getName())
                        .articleCode(key.getDocCode())
                        .labelId(param.getLabelId())
                        .itemNum(getSortNumFromContent(key.getContent()))
                        .itemContent(key.getContent())
                        .itemExplain(key.getItemDesc())
                        .remark(explain.toString())
                        .incompatible(nonMatchExplain.toString())
                        .result(judgeMap.get(RESULT))
                        .riskLevel(judgeMap.get(RISK))
                        .itemId(key.getLawItemId())
                        .systemId(systemId)
                        .score(score)
                        .riskDesc(CollUtil.join(nonMatchRiskDescDuplicateSet, StrPool.LF))
                        .build();
                coLegalList.add(coLegal);
            });
        });
        List<CoLegal> res = new ArrayList<>();
        coLegalList.stream().collect(Collectors.groupingBy(CoLegal::getItemId)).forEach((lawItemId, values) -> {
            List<OptimalFormDTO> collect = values.stream().map(
                    v -> OptimalFormDTO.builder().riskLevel(v.getRiskLevel()).description(v.getRemark())
                        .result(v.getResult()).incompatible(v.getIncompatible()).systemId(v.getSystemId()).score(v.getScore()).build())
                .collect(Collectors.toList());
            OptimalFormDTO optimalForm = getOptimalForm(collect, systemMap);
            CoLegal bean = values.get(0);
            bean.setLegalId(SnowFlake.getId());
            bean.setCoSystemResultList(buildLegalSystemResultList(bean.getLegalId(), values));
            bean.setRemark(optimalForm.getDescription());
            bean.setResult(optimalForm.getResult());
            bean.setRiskLevel(optimalForm.getRiskLevel());
            bean.setSystemId(LabelEnum.XTDY.getCode());
            bean.setSystemResult(optimalForm.getSystemResult());
            bean.setScore(optimalForm.getScore());
            bean.setIncompatible(optimalForm.getIncompatible());
            res.add(bean);
        });

        // 删除专项结果
        if (needCalc) {
            List<SpecCalcResult> specCalcResults = new ArrayList<>();
            specCalcSystemResults.stream().filter(specCalcSystemResultDTO -> specCalcSystemResultDTO.getScore() != null)
                .collect(Collectors.groupingBy(SpecCalcSystemResultDTO::getBpCode)).forEach((bpCode, values) -> {
                    SpecCalcResult specCalcResult = new SpecCalcResult();
                    SpecCalcSystemResultDTO specCalcSystemResultDTO = values.get(0);
                    BeanUtils.copyProperties(specCalcSystemResultDTO, specCalcResult);
                    specCalcResult.setScore(BigDecimal.valueOf(
                        values.stream().mapToDouble(s -> s.getScore().doubleValue()).average().orElse(0)));
                    specCalcResults.add(specCalcResult);
                });
            QueryWrapper<SpecCalcResult> delWrapper = new QueryWrapper<>();
            delWrapper.eq("operation_id", param.getOperationId());
            specCalcResultService.remove(delWrapper);
            specCalcResultService.saveBatch(specCalcResults);
        }
        return res;
    }

    private List<CoSystemResult> buildLegalSystemResultList(String relId, List<CoLegal> values) {
        List<CoSystemResult> results =  values.stream().map(coLegal ->
            CoSystemResult.builder()
                .relId(relId)
                .operationId(coLegal.getOperationId())
                .systemId(coLegal.getSystemId())
                .systemResult(coLegal.getResult())
                // 完全符合或者不适用，无风险描述
                .riskDesc((OptEnum.A.getInfo().equals(coLegal.getResult()) ||  OptEnum.D.getInfo().equals(coLegal.getResult())) ? "" : coLegal.getRiskDesc())
                .description(coLegal.getRemark())
                .type(AdviceTypeEnum.LEGAL.getCode())
                .incompatible(coLegal.getIncompatible())
                .build())
            .collect(Collectors.toList());
        List<CoSystemResult> coSystemResultList = new ArrayList<>();
        results.stream().collect(Collectors.groupingBy(CoSystemResult::getSystemId)).forEach((systemId, list)->{
            CoSystemResult coSystemResult = list.get(0);
            Set<String> resSet = new HashSet<>();
            StringBuilder riskDb = new StringBuilder();
            StringBuilder yesSb = new StringBuilder();
            StringBuilder noSb = new StringBuilder();
            list.forEach(coSystemResult1 -> {
                resSet.add(coSystemResult1.getSystemResult());
                riskDb.append(coSystemResult1.getRiskDesc()).append(StrPool.LF);
                if (OptEnum.A.getInfo().equals(coSystemResult1.getSystemResult())){
                    yesSb.append(coSystemResult1.getDescription());
                } else if (OptEnum.C.getInfo().equals(coSystemResult1.getSystemResult())) {
                    noSb.append(coSystemResult1.getIncompatible());
                } else {
                    yesSb.append(coSystemResult1.getDescription());
                    noSb.append(coSystemResult1.getIncompatible());
                }
            });
            coSystemResult.setSystemResult(judgeSystemResult(resSet));
            coSystemResult.setDescription(yesSb.toString());
            coSystemResult.setIncompatible(noSb.toString());
            coSystemResult.setRiskDesc(riskDb.deleteCharAt(riskDb.lastIndexOf(StrPool.LF)).toString());
            coSystemResultList.add(coSystemResult);
        });
        return coSystemResultList;
    }

    private String judgeSystemResult(Set<String> resSet){
        String result = "";
        if (resSet.size() == 1){
            result = resSet.stream().findFirst().get();
        } else if (resSet.size() > 1){
            Set<String> set = resSet.stream().filter(s->!s.equals(OptEnum.D.getInfo())).collect(Collectors.toSet());
            if (set.size() == 1){
                result = resSet.stream().findFirst().get();
            } else {
                result = OptEnum.B.getInfo();
            }
        } else {
            result = OptEnum.B.getInfo();
        }
        return result;
    }

    private List<CoSystemResult> buildCapacitySystemResultList(String relId, List<CoVerification> values) {
        List<CoSystemResult> results = values.stream().map(coVerification ->
                CoSystemResult.builder()
                    .relId(relId)
                    .operationId(coVerification.getOperationId())
                    .systemId(coVerification.getSystemId())
                    .systemResult(coVerification.getResult())
                    // 完全符合或者不适用，无风险描述
                    .riskDesc((OptEnum.A.getInfo().equals(coVerification.getResult()) ||  OptEnum.D.getInfo().equals(coVerification.getResult())) ? "" : coVerification.getRiskDesc())
                    .description(coVerification.getDesc())
                    .type(AdviceTypeEnum.CAPACITY.getCode())
                    .incompatible(coVerification.getIncompatible())
                    .build())
            .collect(Collectors.toList());
        List<CoSystemResult> coSystemResultList = new ArrayList<>();
        results.stream().collect(Collectors.groupingBy(CoSystemResult::getSystemId)).forEach((systemId, list)->{
            CoSystemResult coSystemResult = list.get(0);
            Set<String> resSet = new HashSet<>();
            StringBuilder riskDb = new StringBuilder();
            StringBuilder yesSb = new StringBuilder();
            StringBuilder noSb = new StringBuilder();
            list.forEach(coSystemResult1 -> {
                resSet.add(coSystemResult1.getSystemResult());
                riskDb.append(coSystemResult1.getRiskDesc()).append(StrPool.LF);
                if (OptEnum.A.getInfo().equals(coSystemResult1.getSystemResult())){
                    yesSb.append(coSystemResult1.getDescription());
                } else if (OptEnum.C.getInfo().equals(coSystemResult1.getSystemResult())) {
                    noSb.append(coSystemResult1.getIncompatible());
                } else {
                    yesSb.append(coSystemResult1.getDescription());
                    noSb.append(coSystemResult1.getIncompatible());
                }
            });
            coSystemResult.setSystemResult(judgeSystemResult(resSet));
            coSystemResult.setDescription(yesSb.toString());
            coSystemResult.setIncompatible(noSb.toString());
            coSystemResult.setRiskDesc(riskDb.deleteCharAt(riskDb.lastIndexOf(StrPool.LF)).toString());
            coSystemResultList.add(coSystemResult);
        });
        return coSystemResultList;
    }

    // 2. 添加核查项筛选方法
    private QuestionnaireContent filterQuestionnaireContent(List<QuestionnaireContent> contents, String fileConditionTags, Boolean hasException, FileAnalysisLogicDTO analysisLogicDTO) {
        if (CollUtil.isEmpty(contents))
            return null;

        //  如果没有例外情况或者例外条件为空，核查项列表中如果有未勾选的核查项，则返回；都勾选了返回第一个
        if (Objects.isNull(analysisLogicDTO) || !hasException || StrUtil.isEmpty(fileConditionTags)) {
            // 优先返回未勾选的核查项
            return contents.stream()
                    .filter(content -> !content.getChecked())
                    .findFirst()
                    .orElse(contents.get(0));
        }

        switch (analysisLogicDTO.getExceptionType()) {
            case MIDDLE:
                return contents.stream()
                        .filter(content -> !content.getChecked())
                        .findFirst()
                        .orElse(contents.get(0));
            case CONDITION:
                // 场景2: 条文有标签要求
                Set<String> lawTags = new HashSet<>(Arrays.asList(fileConditionTags.split(StrUtil.COMMA)));

                // 找到标签匹配度最高的核查项
                return contents.stream()
                        .map(content -> {
                            Set<String> contentTags = StrUtil.isEmpty(content.getConditionTags()) ?
                                    new HashSet<>() : new HashSet<>(Arrays.asList(content.getConditionTags().split(StrUtil.COMMA)));
                            // 检查核查项标签是否包含所有条文标签
                            if (!contentTags.containsAll(lawTags)) {
                                return new AbstractMap.SimpleEntry<>(content, -1); // 不完全包含，标记为-1
                            }
                            // 计算匹配的标签数
                            long matchCount = contentTags.stream().filter(lawTags::contains).count();
                            return new AbstractMap.SimpleEntry<>(content, (int)matchCount);
                        })
                        .filter(entry -> entry.getValue() >= 0) // 过滤掉不完全包含条文标签的核查项
                        .max(Map.Entry.comparingByValue())
                        .map(Map.Entry::getKey)
                        .orElse(null); // 如果没有匹配的核查项，返回null
            default:
                throw new ServiceException("不支持的例外分析类型");
        }
    }

    private BigDecimal scoreJudgeScore(double count, double total, FileAnalysisLogic fileAnalysisLogic, boolean hasInCompliant) {
        if (hasInCompliant)
            return BigDecimal.ZERO;
        BigDecimal zero = BigDecimal.ZERO;
        if (total <= 0) {
            return zero;
        }
        Boolean hasException = fileAnalysisLogic.getHasException();
        double v = count / total;
        if (v >= 1) {
            return BigDecimal.ONE;
        } else if (hasException && v > 0) {
            return BigDecimal.valueOf(0.5);
        }
        return zero;
    }

    private Map<String, String> judgeException(double count, double total, FileAnalysisLogic fileAnalysisLogic, FileAnalysisLogicDTO analysisLogicDTO, boolean hasInCompliant) {
        Map<String, String> res = new HashMap<>(2);
        if (hasInCompliant) {
            res.put(RESULT, OptEnum.C.getInfo());
            res.put(RISK, RiskLevelEnum.HIGH.getInfo());
            res.put(RATE, "1");
            return res;
        }
        MiddleDTO dto = (MiddleDTO) JSON.parseObject(analysisLogicDTO.getAttributes().toString(), analysisLogicDTO.getExceptionType().getClazz());
        Boolean hasException = fileAnalysisLogic.getHasException();
        // 全部都不适用
        if (total <= 0) {
            res.put(RESULT, OptEnum.D.getInfo());
            res.put(RISK, RiskLevelEnum.NONE.getInfo());
            res.put(RATE, "0");
            return res;
        }
        double v = count / total;
        res.put(RATE, String.valueOf(v));
        if (v >= 1) {
            res.put(RESULT, OptEnum.A.getInfo());
            res.put(RISK, RiskLevelEnum.LOW.getInfo());
        } else if (hasException && v >= 0.5) {
            res.put(RESULT, dto.getStatusName());
            res.put(RISK, RiskLevelEnum.MEDIUM.getInfo());
        } else {
            // total != 0 count = 0
            res.put(RESULT, OptEnum.C.getInfo());
            res.put(RISK, RiskLevelEnum.HIGH.getInfo());
        }
        return res;
    }

    private double calculateCount(FileAnalysisLogicDTO fileAnalysisLogicDTO, Boolean hasException, String itemId, Map<String, Integer> itemDegreeMap,
                                  String fileItemConditionTags, String contentConditionTags) {
        // 文件无需例外分析
        if (Objects.isNull(fileAnalysisLogicDTO) || !fileAnalysisLogicDTO.getHasException())
            return 1;
        // 条目无需例外分析
        if (!hasException)
            return 1;
        FileAnalysisType exceptionType = fileAnalysisLogicDTO.getExceptionType();
        double flag = 1;
        switch (exceptionType) {
            case MIDDLE:
                MiddleDTO middleDTO = (MiddleDTO) JSON.parseObject(fileAnalysisLogicDTO.getAttributes().toString(), exceptionType.getClazz());
                ConditionType conditionType = ConditionType.valueOf(middleDTO.getBasic());
                IntervalDTO intervalDTO = (IntervalDTO) JSON.parseObject(fileAnalysisLogicDTO.getAttributes().toString(), conditionType.getClazz());
                // 符合程度前台只有汽车调研模板才展示，其他模板不展示，勾选默认100，即itemDegreeMap为null
                int degree = Objects.isNull(itemDegreeMap) ? 100 : itemDegreeMap.get(itemId);
                List<IntervalDTO.Item> conditions = intervalDTO.getConditions();
                // 0 : 不符合 0.5: 中间状态 1: 符合
                for (IntervalDTO.Item condition : conditions) {
                    String operator = condition.getOperator();
                    Integer numeric = condition.getNumeric();
                    // 符合程度为100且不是EQ
                    if (degree == 100 && !Objects.equals(OperateType.EQ.getDesc(), operator))
                        continue;
                    if (Objects.equals(OperateType.EQ.getDesc(), operator)) {
                        if (!Objects.equals(numeric, degree))
                            flag = 0;
                        else
                            flag = 0.5;
                    } else if (Objects.equals(OperateType.GT.getDesc(), operator)) {
                        if (degree > numeric)
                            flag = 0.5;
                        else
                            flag = 0;
                    } else if (Objects.equals(OperateType.LT.getDesc(), operator)) {
                        if (degree < numeric)
                            flag = 0.5;
                        else
                            flag = 0;
                    } else if (Objects.equals(OperateType.GE.getDesc(), operator)) {
                        if (degree >= numeric)
                            flag = 0.5;
                        else
                            flag = 0;
                    } else if (Objects.equals(OperateType.LE.getDesc(), operator)) {
                        if (degree <= numeric)
                            flag = 0.5;
                        else
                            flag = 0;
                    }
                    if (flag == 0)
                        return flag;
                }
                break;
            case CONDITION:
                List<String> contentTags = StrUtil.split(contentConditionTags, StrUtil.COMMA);
                List<String> fileItemTags = StrUtil.split(fileItemConditionTags, StrUtil.COMMA);
                // 核查项条件标签是否包含文件条目配置的所有标签
                if (!new HashSet<>(contentTags).containsAll(fileItemTags))
                    flag = 0;
                break;
        }
        return flag;
    }

    private FileAnalysisLogicDTO analysisCondition(FileAnalysisLogic fileAnalysisLogic) {
        if (Objects.nonNull(fileAnalysisLogic)) {
            FileAnalysisLogicDTO dto = new FileAnalysisLogicDTO();
            dto.setAnalysisType(fileAnalysisLogic.getAnalysisType());
            dto.setHasException(fileAnalysisLogic.getHasException());
            FileAnalysisType exceptionType = Objects.nonNull(fileAnalysisLogic.getExceptionType()) ? FileAnalysisType.valueOf(fileAnalysisLogic.getExceptionType()) : null;
            dto.setExceptionType(exceptionType);
            if (Objects.nonNull(exceptionType))
                dto.setAttributes(fileAnalysisLogic.getAttributes());
            return dto;
        }
        return null;
    }

    private int getSortNumFromContent(String content) {
        String spaceBefore = StrUtil.sub(content, 0, 9);
        String number = StrUtil.subBetween(spaceBefore, "第", "条");
        if (StrUtil.isEmpty(number)) {
            return 999;
        }
        return NumberChineseFormatter.chineseToNumber(number);
    }

    @Override
    @SchemaSwitch(value = AnalysisParam.class)
    public List<CoVerification> abilityAnalysis(AnalysisParam param) {
        final Map<Long, String> systemMap = getBusSystemMap(param.getOperationId());
        // 查询能力分析模板关联的核查项
        final Map<AnalysisStandardVO, List<AnalysisStandardItemVO>> analysisStandardMap =
            analysisTemplateMapper.qryByTemplateType(1, param.getLevelList(), param.getTemplateId()).stream()
                .collect(Collectors.groupingBy(l -> BeanUtil.copyProperties(l, AnalysisStandardVO.class)));
        final Map<Long, Map<String, List<QuestionnaireContent>>> contentMap = getContentMap(param.getOperationId(), systemMap);

        // 判断是否需要判分
        boolean needCalc;
        if (param.getIsSpec() != null && param.getIsSpec()) {
            SpecialEvaluationConfig specialEvaluationConfig = specialEvaluationConfigMapper.selectById(param.getSpecId());
            needCalc = "ANALYSIS".equals(specialEvaluationConfig.getCalcTemplate());
        } else {
            needCalc = false;
        }

        // 判断作业是否存在现状核验
        boolean containsVerification = checkContainsLabel(param.getOperationId(), LabelEnum.XZHY.getCode());

        final Map<Integer, FileAnalysisLogic> analysisMap = fileAnalysisLogicMapper.selectList(
                new QueryWrapper<FileAnalysisLogic>().eq("file_type", "STANDARD")).stream().collect(Collectors.toMap(FileAnalysisLogic::getFileId, Function.identity()));
        final Map<String, Integer> itemDegreeMap = questionnaireItemDegreeServiceImpl.list(new QueryWrapper<QuestionnaireItemDegree>()
                .eq("operation_id", param.getOperationId())).stream().collect(Collectors.toMap(QuestionnaireItemDegree::getItemId, QuestionnaireItemDegree::getDegree, Math::max));
        final Map<Integer, FileAnalysisLogicDTO> analysisConditionMap = new HashMap<>();

        // 获取专项评估判断条件
        List<SpecialEvaluationConditionConfig> conditionConfigs = specialEvaluationConditionConfigMapper.selectList(
            new QueryWrapper<SpecialEvaluationConditionConfig>().eq("evaluation_id", param.getSpecId()).eq("type", 1));
        // 是否有大部分符合
        boolean exist = conditionConfigs.stream()
            .anyMatch(specialEvaluationConditionConfig -> "0.7".equals(specialEvaluationConditionConfig.getResult()));
        List<CoVerification> verification = new ArrayList<>();
        List<SpecCalcSystemResultDTO> specCalcSystemResults = new ArrayList<>();
        analysisStandardMap.forEach((key, value1) -> {
            List<AnalysisStandardItemVO> value = value1.stream().filter(v -> Objects.nonNull(v.getItemId())).collect(Collectors.toList());
            // 是否需要进行例外分析
            final FileAnalysisLogic fileAnalysisLogic = analysisMap.get(key.getStandardId());
            analysisConditionMap.putIfAbsent(key.getStandardId(), analysisCondition(fileAnalysisLogic));
            FileAnalysisLogicDTO analysisLogicDTO = analysisConditionMap.get(key.getStandardId());
            // 每个系统单独计算
            contentMap.forEach((systemId, questionnaires) -> {
                double total = value.size();
                double count = 0.0;
                // 是否存在不符合的核查项，whether条件专用
                boolean hasInCompliant = false;
                // 是否为whether例外情况,whether只要有不符合的就不符合
                boolean whetherException = Objects.nonNull(analysisLogicDTO) && Objects.equals(analysisLogicDTO.getAnalysisType(), "WHETHER");
                StringBuilder explain = new StringBuilder();
                StringBuilder nonMatchExplain = new StringBuilder();
                Set<String> duplicateSet = new HashSet<>();
                Set<String> nonMatchDuplicateSet = new HashSet<>();
                Set<String> nonMatchRiskDescDuplicateSet = new HashSet<>();
                for (AnalysisStandardItemVO item : value) {
                    List<QuestionnaireContent> contentList = questionnaires.get(item.getItemId());
                    QuestionnaireContent content = filterQuestionnaireContent(contentList, key.getConditionTags(), key.getHasException(), analysisLogicDTO);
                    if (Objects.nonNull(content)) {
                        // 不适用，排除分母中的数量
                        if (Objects.nonNull(content.getInapplicable()) && content.getInapplicable()) {
                            total--;
                            continue;
                        }
                        // 不为反例，符合数加一
                        if (content.getChecked() && (Objects.isNull(content.getReverse()) || !content.getReverse())) {
                            double itemScore = calculateCount(analysisLogicDTO, key.getHasException(), item.getItemId(), itemDegreeMap, key.getConditionTags(), content.getConditionTags());
                            if (whetherException && !hasInCompliant) {
                                // 判断whether例外情况中是否有不符合的检查项，只需要判断一次
                                hasInCompliant = itemScore == 0;
                            }
                            count += itemScore;
                            if (containsVerification) {
                                if (!duplicateSet.contains(content.getExplain())) {
                                    explain.append(content.getExplain()).append(";");
                                    duplicateSet.add(content.getExplain());
                                }
                            } else {
                                if (!duplicateSet.contains(content.getItemTitle())) {
                                    explain.append(content.getItemTitle()).append(";");
                                    duplicateSet.add(content.getItemTitle());
                                }
                            }
                        } else {
                            // 若包含现状核验，则取现状核验中的描述说明
                            if (containsVerification) {
                                if (StrUtil.isNotEmpty(content.getExplain()) && !nonMatchDuplicateSet.contains(content.getExplain())) {
                                    nonMatchExplain.append(content.getExplain()).append(";");
                                    nonMatchDuplicateSet.add(content.getExplain());
                                }
                            } else {
                                if (StrUtil.isNotEmpty(item.getDescribe()) && !nonMatchDuplicateSet.contains(item.getDescribe())) {
                                    nonMatchExplain.append(item.getDescribe()).append(";");
                                    nonMatchDuplicateSet.add(item.getDescribe());
                                    nonMatchRiskDescDuplicateSet.add(item.getRiskDesc());
                                }
                            }
                            // 如果是whether例外情况，且调研问卷没有勾选该核查项
                            if (whetherException)
                                hasInCompliant = true;
                        }
                    } else {
                        total--;
                    }
                }

                // 判定符合性
                Map<String, String> judgeMap;
                BigDecimal score;
                if (needCalc){
                    // 专项评估增加大部分符合判定
                    judgeMap = judgeForSpec(count, total, exist);
                    String res = calcSpecScore(count, total, conditionConfigs);
                    score = StrUtil.isEmpty(res) ? null : BigDecimal.valueOf(Double.parseDouble(res));
                    log.info("count:{},total:{},score:{}",count,total,score);
                    SpecCalcSystemResultDTO specCalcSystemResultDTO =
                        SpecCalcSystemResultDTO.builder()
                            .bpCode(key.getBpCode())
                            .process(key.getProcess())
                            .stage(key.getStage())
                            .operationId(param.getOperationId())
                            .score(score)
                            .level(key.getLevel() != null ? Integer.valueOf(key.getLevel()) : null)
                            .standardId(key.getStandardId())
                            .type("ANALYSIS")
                            .build();
                    specCalcSystemResults.add(specCalcSystemResultDTO);
                } else if (whetherException) {
                    judgeMap = judgeException(count, total, fileAnalysisLogic, analysisLogicDTO, hasInCompliant);
                    score = scoreJudgeScore(count, total, fileAnalysisLogic, hasInCompliant);
                } else {
                    judgeMap = judge(count, total);
                    score = judgeScore(count, total);
                }

                CoVerification coVerification = CoVerification.builder()
                        .operationId(param.getOperationId())
                        .labelId(param.getLabelId())
                        .bpCode(key.getBpCode())
                        .gpDimension(getGpDimensionValue(key))
                        .standardProvision(key.getContent())
                        .description(explain.toString())
                        .incompatible(nonMatchExplain.toString())
                        .result(judgeMap.get(RESULT))
                        .modelName(key.getName())
                        .level(key.getLevel())
                        .type(key.getDimension())
                        .classify(key.getClassify())
                        .score(score)
                        .systemId(systemId)
                        .regulatoryFactor(Objects.nonNull(key.getEnableFactor()) ? key.getDefaultRegulatoryFactor() : null)
                        .stage(key.getStage())
                        .process(key.getProcess())
                        .analysisId(param.getTemplateId())
                        .standardId(key.getStandardId())
                        .itemRate(BigDecimal.valueOf(Double.parseDouble(judgeMap.get(RATE))))
                        .sort(key.getSort())
                        .riskDesc(CollUtil.join(nonMatchRiskDescDuplicateSet, StrPool.LF))
                        .build();
                verification.add(coVerification);
            });
        });
        List<CoVerification> res = new ArrayList<>();
        // 将不同业务系统相同的BP_CODE数据合并，描述取并集，符合性结果取更优的结果（例如一个系统部分符合，一个系统完全符合，该BP_CODE取完全符合）
        // 以下修改（20240122）：
        // 不适用 + 不适用 = 不适用
        // 完全符合 + 完全符合 = 完全符合
        // 不符合 + 不符合 = 不符合
        // 不适用 + 不符合 = 不符合
        // 其它 = 部分符合
        verification.stream().collect(Collectors.groupingBy(v -> v.getStandardId() + v.getBpCode())).forEach((bpCode, values) -> {
            List<OptimalFormDTO> collect = values.stream().map(v -> OptimalFormDTO.builder().systemId(v.getSystemId())
                    .description(v.getDescription()).result(v.getResult()).incompatible(v.getIncompatible()).score(v.getScore()).build()).collect(Collectors.toList());
            OptimalFormDTO optimalForm = getOptimalForm(collect, systemMap);
            CoVerification bean = values.get(0);
            bean.setVerificationId(SnowFlake.getId());
            bean.setCoSystemResultList(buildCapacitySystemResultList(bean.getVerificationId(), values));
            bean.setResult(optimalForm.getResult());
            bean.setDescription(optimalForm.getDescription());
            bean.setSystemResult(optimalForm.getSystemResult());
            bean.setSystemId(LabelEnum.XTDY.getCode());
            bean.setScore(optimalForm.getScore());
            bean.setIncompatible(optimalForm.getIncompatible());
            res.add(bean);
        });
        QueryWrapper<CoVerification> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("operation_id", param.getOperationId());
        coVerificationMapper.delete(deleteWrapper);
        // 删除专项结果
        if (needCalc) {
            List<SpecCalcResult> specCalcResults = new ArrayList<>();
            specCalcSystemResults.stream().filter(specCalcSystemResultDTO -> specCalcSystemResultDTO.getScore() != null)
                .collect(Collectors.groupingBy(SpecCalcSystemResultDTO::getBpCode)).forEach((bpCode, values) -> {
                    SpecCalcResult specCalcResult = new SpecCalcResult();
                    SpecCalcSystemResultDTO specCalcSystemResultDTO = values.get(0);
                    BeanUtils.copyProperties(specCalcSystemResultDTO, specCalcResult);
                    specCalcResult.setScore(BigDecimal.valueOf(
                        values.stream().mapToDouble(s -> s.getScore().doubleValue()).average().orElse(0)));
                    specCalcResults.add(specCalcResult);
                });
            QueryWrapper<SpecCalcResult> delWrapper = new QueryWrapper<>();
            delWrapper.eq("operation_id", param.getOperationId());
            specCalcResultService.remove(delWrapper);
            specCalcResultService.saveBatch(specCalcResults);
        }
        return res;
    }

    /**
     * 根据配置的维度选择能力项
     */
    private String getGpDimensionValue(AnalysisStandardVO key) {
        return Optional.ofNullable(key.getAssign())
                .map(DIMENSION_MAPPINGS::get)
                .map(mapper -> mapper.apply(key))
                .orElseGet(key::getDimension); // 默认返回dimension
    }

    private boolean checkContainsLabel(String operationId, Long code) {
        CoOperation coOperation = coOperationMapper.selectById(operationId);
        return StrUtil.split(coOperation.getServiceContent(), StrUtil.COMMA).stream().anyMatch(s -> s.equals(String.valueOf(code)));
    }

    private String calcSpecScore(double count, double total, List<SpecialEvaluationConditionConfig> conditionConfigs) {
        // 不适用
        if (total <= 0){
            return CharSequenceUtil.EMPTY;
        }
        double v = count/total;
        List<BetweenBaseRule.InnerBaseRule> innerBaseRules = new ArrayList<>();
        conditionConfigs.forEach(specialEvaluationConditionConfig -> {
            BetweenBaseRule.InnerBaseRule innerBaseRule = new BetweenBaseRule.InnerBaseRule(specialEvaluationConditionConfig);
            innerBaseRules.add(innerBaseRule);
        });
        BetweenBaseRule betweenBaseRule = new BetweenBaseRule(innerBaseRules);
        return betweenBaseRule.accept(BigDecimal.valueOf(v));
    }

    private Map<String, String> judgeForSpec(double count, double total, boolean exist) {
        Map<String, String> res = new HashMap<>(2);
        // 全部都不适用
        if (total <= 0) {
            res.put(RESULT, OptEnum.D.getInfo());
            res.put(RISK, RiskLevelEnum.NONE.getInfo());
            res.put(RATE, "0");
            return res;
        }
        double v = count / total;
        res.put(RATE, String.valueOf(v));
        if (exist) {
            if (v == 1) {
                res.put(RESULT, OptEnum.A.getInfo());
                res.put(RISK, RiskLevelEnum.LOW.getInfo());
            } else if (v <= 0.5 && v > 0) {
                res.put(RESULT, OptEnum.B.getInfo());
                res.put(RISK, RiskLevelEnum.MEDIUM.getInfo());
            } else if (v < 1 && v > 0.5) {
                res.put(RESULT, OptEnum.E.getInfo());
                res.put(RISK, RiskLevelEnum.MEDIUM.getInfo());
            } else {
                // total != 0 count = 0
                res.put(RESULT, OptEnum.C.getInfo());
                res.put(RISK, RiskLevelEnum.HIGH.getInfo());
            }
        } else {
            if (v == 1) {
                res.put(RESULT, OptEnum.A.getInfo());
                res.put(RISK, RiskLevelEnum.LOW.getInfo());
            } else if (v < 1 && v > 0) {
                res.put(RESULT, OptEnum.B.getInfo());
                res.put(RISK, RiskLevelEnum.MEDIUM.getInfo());
            } else {
                // total != 0 count = 0
                res.put(RESULT, OptEnum.C.getInfo());
                res.put(RISK, RiskLevelEnum.HIGH.getInfo());
            }
        }
        return res;
    }

    private Map<Long, Map<String, List<QuestionnaireContent>>> getContentMap(String param, Map<Long, String> systemMap) {
        Map<Long, Map<String, List<QuestionnaireContent>>> contentMap = new HashMap<>();
        if (CollUtil.isEmpty(systemMap)) {
            // 适应旧数据
            contentMap.put(LabelEnum.XTDY.getCode(), questionnaireContentMapper.selectAvailableItems(
                    param).stream().collect(Collectors.groupingBy(QuestionnaireContent::getItemId)));
        } else {
            Map<Long, List<QuestionnaireContent>> groupMap = questionnaireContentMapper.selectCheckedItems(param).stream()
                    .collect(Collectors.groupingBy(QuestionnaireContent::getObjectId));
            for (Long busSystemId : systemMap.keySet()) {
                // 具体的系统调研问卷
                List<QuestionnaireContent> questionnaireContents = groupMap.get(busSystemId);
                List<QuestionnaireContent> organizeQuestionnaire = groupMap.get(LabelEnum.ZZDY.getCode());
                if (CollUtil.isEmpty(questionnaireContents)) {
                    if (CollUtil.isEmpty(organizeQuestionnaire)) {
                        contentMap.put(busSystemId, new HashMap<>());
                    } else {
                        // 系统调研为空，全选 ‘以上均不符合选项’ 别忘记加上组织调研内容
                        contentMap.put(busSystemId, organizeQuestionnaire.stream().collect(Collectors.groupingBy(QuestionnaireContent::getItemId)));
                    }
                } else {
                    // 每个系统调研问卷都要加上组织调研问卷
                    if (CollUtil.isNotEmpty(organizeQuestionnaire)) {
                        questionnaireContents.addAll(organizeQuestionnaire);
                    }
                    contentMap.put(busSystemId, questionnaireContents.stream().collect(Collectors.groupingBy(QuestionnaireContent::getItemId)));
                }
            }
        }
        return contentMap;
    }

    private OptimalFormDTO getOptimalForm(List<OptimalFormDTO> results, Map<Long, String> systemMap) {
        Map<Long, List<String>> duplicateDescMap = new HashMap<>();
        Map<Long, List<String>> duplicateIncompatibleMap = new HashMap<>();
        Set<String> mergeResultSet = new HashSet<>();
        Map<Long, StringBuilder> mergeDescMap = new TreeMap<>(Comparator.naturalOrder());
        Map<Long, StringBuilder> mergeIncompatibleMap = new TreeMap<>(Comparator.naturalOrder());
        final StringBuilder mergeResult = new StringBuilder();
        String finalResult = getResultString(results);
        boolean flag = (Objects.equals(finalResult, OptEnum.A.getInfo()) || Objects.equals(finalResult, OptEnum.C.getInfo()));
        results.stream()
                .collect(Collectors.groupingBy(
                        OptimalFormDTO::getSystemId,
                        TreeMap::new, // 使用 TreeMap 自动按 systemId 升序排序
                        Collectors.toList()
                )).forEach((systemId, values) -> {
            String busSystem = systemMap.get(systemId);
            String systemComment = flag ? StrUtil.EMPTY : busSystem + Constants.SYSTEM_SUFFIX;
            String resultString = getResultString(values);
            List<String> duplicateDescSet = duplicateDescMap.getOrDefault(systemId, new ArrayList<>());
            List<String> duplicateIncompatibleSet = duplicateIncompatibleMap.getOrDefault(systemId, new ArrayList<>());
            for (OptimalFormDTO value : values) {
                if (StrUtil.isNotEmpty(value.getDescription())) {
                    StringBuilder descBuilder = mergeDescMap.computeIfAbsent(systemId, k -> new StringBuilder(systemComment));
                    if (!flag) {
                        if (!duplicateDescSet.contains(value.getDescription())) {
                            descBuilder.append(value.getDescription());
                            duplicateDescSet.add(value.getDescription());
                        }
                    } else {
                        if (!mergeResultSet.contains(value.getDescription())) {
                            descBuilder.append(value.getDescription());
                            mergeResultSet.add(value.getDescription());
                        }
                    }
                }
                if (StrUtil.isNotEmpty(value.getIncompatible())) {
                    StringBuilder incompatibleBuilder = mergeIncompatibleMap.computeIfAbsent(systemId, k -> new StringBuilder(systemComment));
                    if (!flag) {
                        if (!duplicateIncompatibleSet.contains(value.getIncompatible())) {
                            incompatibleBuilder.append(value.getIncompatible());
                            duplicateIncompatibleSet.add(value.getIncompatible());
                        }
                    } else {
                        if (!mergeResultSet.contains(value.getIncompatible())) {
                            incompatibleBuilder.append(value.getIncompatible());
                            mergeResultSet.add(value.getIncompatible());
                        }
                    }
                }
            }
            mergeResult.append(busSystem).append(Constants.SYSTEM_SUFFIX).append(resultString).append(StrUtil.LF);
        });
        StringBuilder finalMergeResult;
        // 特殊情况：若业务系统和bpcode分别只有一个，则直接取最终结果
        if (systemMap.size() == 1){
            finalMergeResult = new StringBuilder(systemMap.values().iterator().next()).append(Constants.SYSTEM_SUFFIX).append(finalResult);
        } else {
            finalMergeResult = mergeResult;
        }
        String mergeDesc = mergeDescMap.values().stream().map(StringBuilder::toString).collect(Collectors.joining(StrUtil.LF));
        String mergeIncompatible = mergeIncompatibleMap.values().stream().map(StringBuilder::toString).collect(Collectors.joining(StrUtil.LF));
        // 多系统取最优值
        Optional<OptimalFormDTO> optimalFormDTO = results.stream().filter(optimal -> optimal.getScore() != null)
                .max(Comparator.comparing(OptimalFormDTO::getScore));
        return OptimalFormDTO.builder()
                .description(mergeDesc)
                .incompatible(mergeIncompatible)
                .result(finalResult)
                .score(optimalFormDTO.map(OptimalFormDTO::getScore).orElse(null))
                .systemResult(finalMergeResult.toString())
                .riskLevel(getRiskLevelByResult(finalResult))
                .build();
    }

    private static String getResultString(List<OptimalFormDTO> results) {
        Set<String> resultSet = results.stream().map(OptimalFormDTO::getResult).collect(Collectors.toSet());
        String finalResult;
        if (resultSet.size() == 1){
            finalResult = resultSet.iterator().next();
        } else if (resultSet.size() > 1 && resultSet.contains(OptEnum.C.getInfo())
                && resultSet.contains(OptEnum.D.getInfo())
                && !resultSet.contains(OptEnum.A.getInfo())
                && !resultSet.contains(OptEnum.B.getInfo())){
            // 不符合 + 不适用 = 不符合
            finalResult = OptEnum.C.getInfo();
        } else if (resultSet.size() > 1 && resultSet.contains(OptEnum.A.getInfo())
                && resultSet.contains(OptEnum.D.getInfo())
                && !resultSet.contains(OptEnum.C.getInfo())
                && !resultSet.contains(OptEnum.B.getInfo())) {
            // 完全符合 + 不适用 = 完全符合
            finalResult = OptEnum.A.getInfo();
        } else {
            finalResult = OptEnum.B.getInfo();
        }
        return finalResult;
    }

    private String getRiskLevelByResult(String finalResult) {
        if (Objects.equals(OptEnum.A.getInfo(), finalResult))
            return RiskLevelEnum.LOW.getInfo();
        if (Objects.equals(OptEnum.B.getInfo(), finalResult))
            return RiskLevelEnum.MEDIUM.getInfo();
        if (Objects.equals(OptEnum.C.getInfo(), finalResult))
            return RiskLevelEnum.HIGH.getInfo();
        if (Objects.equals(OptEnum.D.getInfo(), finalResult))
            return RiskLevelEnum.NONE.getInfo();
        return RiskLevelEnum.LOW.getInfo();
    }

    @Override
    @SchemaSwitch(value = AnalysisCharReq.class)
    public List<CoGapAnalysis> gapAnalysis(AnalysisCharReq param, Function<CoVerification, String> func,
        List<CoVerification> coVerificationList) {
        AnalysisTemplate analysisTemplate = analysisTemplateMapper.selectById(param.getTemplateId());
        Map<String, List<CoVerification>> coVerificationMap = coVerificationList.stream()
            .filter(coVerification -> param.getStandardId().equals(coVerification.getStandardId()))
            .filter(new MyPredicate(param.getGranularity())).collect(Collectors.groupingBy(func));
        return gapAnalysesCommon(param, analysisTemplate, coVerificationMap, 1);
    }

    @Override
    @SchemaSwitch(value = AnalysisCharReq.class)
    public List<CoGapAnalysis> comprehensiveGapAnalysis(AnalysisCharReq param, List<CoVerification> coVerificationList) {
        AnalysisTemplate analysisTemplate = analysisTemplateMapper.selectById(param.getTemplateId());
        Map<String, List<CoVerification>> analysisStandardMap =
            coVerificationList.stream().filter(coVerification -> param.getStandardId().equals(coVerification.getStandardId()))
                .peek(a -> {
                    if (StrUtil.containsAny(a.getType(), TECH_ABILITY)) {
                        a.setGpDimension("技术能力");
                    } else {
                        a.setGpDimension("管理能力");
                    }
                }).collect(Collectors.groupingBy(CoVerification::getGpDimension));
        return gapAnalysesCommon(param, analysisTemplate, analysisStandardMap, 2);
    }

    @NotNull
    private List<CoGapAnalysis> gapAnalysesCommon(AnalysisCharReq param,AnalysisTemplate analysisTemplate,
        Map<String, List<CoVerification>> coVerificationMap, int type) {
        List<CoGapAnalysis> coGapAnalysisList = new ArrayList<>();
        final String chartId = SnowFlake.getId();
        Map<Integer, String> standardFileKeyValueMap = standardMapper.queryStandardFile(
            Long.valueOf(analysisTemplate.getId()), null, analysisTemplate.getNoDisplay()).stream().collect(Collectors.toMap(Standard::getId, Standard::getIntroduce));
        coVerificationMap.forEach((key, value) -> {
            int total = value.size();
            long countA = value.stream().filter(p -> OptEnum.A.getInfo().equals(p.getResult())).count();
            long countB = value.stream().filter(p -> OptEnum.B.getInfo().equals(p.getResult())).count();
            long countC = value.stream().filter(p -> OptEnum.C.getInfo().equals(p.getResult())).count();
            long countD = value.stream().filter(p -> OptEnum.D.getInfo().equals(p.getResult())).count();
            long countE = value.stream().filter(p -> OptEnum.E.getInfo().equals(p.getResult())).count();
            BigDecimal score = BigDecimal.valueOf(
                value.stream().filter(s -> s.getScore() != null).mapToDouble(s -> s.getScore().doubleValue()).sum());
            // 排序截取第一个
            Integer sort = value.stream().findFirst().orElse(new CoVerification()).getSort();
            // 能力系数
            BigDecimal factor = score.divide(BigDecimal.valueOf(total), 4, RoundingMode.HALF_UP).
                multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            CoGapAnalysis coGapAnalysis =
                CoGapAnalysis.builder()
                    .operationId(param.getOperationId())
                    .labelId(param.getLabelId())
                    .systemId(LabelEnum.XTDY.getCode())
                    .analysisDimension(String.valueOf(analysisTemplate.getId()))
                    .analysisGranularity(param.getGranularity())
                    .analysisChart(param.getChart())
                    .chartId(chartId)
                    .chartName(standardFileKeyValueMap.get(
                        param.getStandardId()) + param.getGranularityName() + param.getChart()).indicator(key)
                    .total(total)
                    .score(score)
                    .factor(factor)
                    .countA((int)countA)
                    .countB((int)(countB+countE))
                    .countC((int)countC)
                    .countD((int)countD)
                    .standardId(param.getStandardId())
                    .type(type)
                    .sort(sort)
                    .build();
            coGapAnalysisList.add(coGapAnalysis);
        });

        return coGapAnalysisList;
    }

    @Async
    @Override
    public RiskAnalysisResultVO riskAnalysisNew(AnalysisParam param) {
        StopWatch watch = new StopWatch();
        watch.start();
        String operationId = param.getOperationId();
        RiskAnalysisResultVO riskAnalysisResultVO = iRiskAnalysisService.riskAnalysis(operationId, param.getLabelId(),param.getSystemId(), false);
        watch.stop();
        log.debug("业务系统ID={}生命周期生成耗时：{}(ms)",param.getSystemId(), watch.getTotalTimeMillis());
        return riskAnalysisResultVO;
    }

    /**
     * 符合性计算和风险等级计算
     */
    private Map<String, String> judge(double count, double total) {
        Map<String, String> res = new HashMap<>(2);
        // 全部都不适用
        if (total <= 0) {
            res.put(RESULT, OptEnum.D.getInfo());
            res.put(RISK, RiskLevelEnum.NONE.getInfo());
            res.put(RATE, "0");
            return res;
        }
        double v = count / total;
        res.put(RATE, String.valueOf(v));
        if (v >= 1) {
            res.put(RESULT, OptEnum.A.getInfo());
            res.put(RISK, RiskLevelEnum.LOW.getInfo());
        } else if (v < 1 && v > 0) {
            res.put(RESULT, OptEnum.B.getInfo());
            res.put(RISK, RiskLevelEnum.MEDIUM.getInfo());
        } else {
            // total != 0 count = 0
            res.put(RESULT, OptEnum.C.getInfo());
            res.put(RISK, RiskLevelEnum.HIGH.getInfo());
        }
        return res;
    }

    private BigDecimal judgeScore(double count, double total) {
        BigDecimal zero = BigDecimal.ZERO;
        if (total <= 0) {
            return zero;
        }
        double v = count / total;
        if (v >= 1) {
            return BigDecimal.ONE;
        }
        if (v > 0) {
            return BigDecimal.valueOf(0.5);
        }
        return zero;
    }

    private int getThreatLevel(double result) {
        int t = 0;
        if (rangeInDefined(result, 0.0, 0.8)) {
            t = 1;
        }
        if (rangeInDefined(result, 0.8, 1.5) && result != 0.8) {
            t = 2;
        }
        if (rangeInDefined(result, 1.5, 2.0) && result != 1.5) {
            t = 3;
        }
        if (rangeInDefined(result, 2.0, 2.5) && result != 2.0) {
            t = 4;
        }
        if (result > 2.5) {
            t = 5;
        }
        return t;
    }

    private boolean rangeInDefined(double current, double min, double max) {
        return Math.max(min, current) == Math.min(current, max);
    }

    private int getMatrix(double param) {
        //根据矩阵计算 安全事件发生损失等级F
        int a = 0;
        if (rangeInDefined(param, 0, 3)) {
            a = 1;
        }
        if (rangeInDefined(param, 3, 6) && Double.parseDouble(String.valueOf(param)) != 3.0) {
            a = 2;
        }
        if (rangeInDefined(param, 6, 10) && Double.parseDouble(String.valueOf(param)) != 6.0) {
            a = 3;
        }
        if (rangeInDefined(param, 10, 16) && Double.parseDouble(String.valueOf(param)) != 10.0) {
            a = 4;
        }
        if (rangeInDefined(param, 16, 25) && Double.parseDouble(String.valueOf(param)) != 16.0) {
            a = 5;
        }
        return a;
    }

    private int getRiskLevel(int riskLevel) {
        int result;
        if (riskLevel > 20) {
            result = 5;
        } else if (riskLevel > 12) {
            result = 4;
        } else if (riskLevel > 8) {
            result = 3;
        } else if (riskLevel > 4) {
            result = 2;
        } else {
            result = 1;
        }
        return result;
    }

    private Map<Long, String> getBusSystemMap(String operationId) {
        return dynamicProcessTreeMapper.selectByOperationIdAndTreeId(operationId, LabelEnum.XTDY.getCode())
                .stream().collect(Collectors.toMap(TreeLabelDTO::getTreeId, TreeLabelDTO::getTreeName));
    }
}
