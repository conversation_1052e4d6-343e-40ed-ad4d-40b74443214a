package com.dcas.system.service;

import com.dcas.common.domain.entity.ScreenDatasource;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dcas.common.model.dto.ScreenDatasourceDTO;
import com.dcas.common.model.vo.ScreenDatasourceVO;
import com.dcas.system.domain.resp.TestConnectionResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * <p>
 * 大屏数源 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IScreenDatasourceService extends IService<ScreenDatasource> {

    PageInfo<ScreenDatasourceVO> page(Integer pageNum, Integer pageSize, String searchValue);

    void saveScreenDatasource(ScreenDatasourceDTO dto);

    void updateScreenDatasource(ScreenDatasourceDTO dto);

    void removeBySystemIds(Integer[] systemIds);

    TestConnectionResponse testConnect(Integer id);

    List<ScreenDatasourceVO> getBusSystemList();

    TestConnectionResponse testConnectBySystemId(Integer systemId);
}
