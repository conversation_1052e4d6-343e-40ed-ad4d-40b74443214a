package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.annotation.SchemaSwitch;
import com.dcas.common.domain.entity.MkAppJob;
import com.dcas.common.domain.entity.PreSourceConfig;
import com.dcas.common.domain.entity.ScreenSummaryResources;
import com.dcas.common.domain.entity.Tag;
import com.dcas.common.enums.SummaryTypeEnum;
import com.dcas.common.mapper.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.model.dto.ItemAbilityDTO;
import com.dcas.common.model.dto.LifeCycleAbilityDTO;
import com.dcas.common.model.dto.ReportConfigDataScopeDTO;
import com.dcas.common.model.dto.TreeLabelDTO;
import com.dcas.common.model.req.AbilityItemReq;
import com.dcas.common.model.vo.ApiNetworkAssetVO;
import com.dcas.common.model.vo.ItemAbilitySummaryVO;
import com.dcas.common.model.vo.ItemAbilityVO;
import com.dcas.common.model.vo.RiskAnalysisReportVO;
import com.dcas.market.app.service.IAppService;
import com.dcas.system.service.CoSystemService;
import com.dcas.system.service.IRiskAnalysisService;
import com.dcas.system.service.IScreenSummaryResourcesService;
import com.dcas.system.service.QuestionnaireService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 大屏统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
@RequiredArgsConstructor
@Service
public class ScreenSummaryResourcesServiceImpl extends ServiceImpl<ScreenSummaryResourcesMapper, ScreenSummaryResources> implements
    IScreenSummaryResourcesService {

    private final TagMapper tagMapper;
    private final QuestionnaireService questionnaireService;
    private final ModelConfigMapper modelConfigMapper;
    private final MkAppJobMapper mkAppJobMapper;
    private final IAppService iAppService;
    private final IRiskAnalysisService iRiskAnalysisService;
    private final CoSystemService coSystemService;
    private final PreSourceConfigMapper preSourceConfigMapper;

    @SchemaSwitch(String.class)
    @Override
    public void updateScreenStatistics(String operationId) {
        String modelName = modelConfigMapper.getByOperationId(operationId);
        // 获取网络资产统计数据
        List<MkAppJob> appJobs = mkAppJobMapper.selectList(
            new QueryWrapper<MkAppJob>().eq("operation_id", operationId).eq("type", 4).eq("status", 2));
        appJobs.forEach(appJob -> {
            ApiNetworkAssetVO apiNetworkAssetVO = iAppService.pcapReportQuery(appJob);
            if (apiNetworkAssetVO != null) {
                List<ScreenSummaryResources> networkAssetList = new ArrayList<>();
                apiNetworkAssetVO.getAssetDetailList().forEach(assetDetail -> {
                    ScreenSummaryResources screenSummaryResources = new ScreenSummaryResources();
                    screenSummaryResources.setName(assetDetail.getDeviceType());
                    screenSummaryResources.setResult(String.valueOf(assetDetail.getAssetNum()));
                    screenSummaryResources.setType(SummaryTypeEnum.NETWORK_ASSET.getType());
                    screenSummaryResources.setSystemId(Math.toIntExact(appJob.getSystemId()));
                    screenSummaryResources.setCreateTime(LocalDateTime.now());
                    networkAssetList.add(screenSummaryResources);
                });
                ScreenSummaryResources screenSummaryResources = new ScreenSummaryResources();
                screenSummaryResources.setName(String.valueOf(appJob.getPlanId()));
                screenSummaryResources.setResult(String.valueOf(apiNetworkAssetVO.getRiskInterfaceNum()));
                screenSummaryResources.setType(SummaryTypeEnum.RISK_API.getType());
                screenSummaryResources.setSystemId(Math.toIntExact(appJob.getSystemId()));
                screenSummaryResources.setCreateTime(LocalDateTime.now());
                this.saveBatch(networkAssetList);
            }
        });

        // 获取风险计算统计结果
        RiskAnalysisReportVO vo = iRiskAnalysisService.queryRiskAnalysisReport(operationId, false);
        RiskAnalysisReportVO.BusSystemRiskSortChart busSystemRiskSortChart = vo.getBusSystemRiskSortChart();
        RiskAnalysisReportVO.ComprehensiveChart comprehensiveChart = vo.getComprehensiveChart();
        if (busSystemRiskSortChart != null && CollUtil.isNotEmpty(busSystemRiskSortChart.getXAxisList())) {
            Map<String, Long> busSystemMap = coSystemService.listByOperationId(operationId).stream().collect(
                Collectors.toMap(TreeLabelDTO::getTreeName, TreeLabelDTO::getTreeId));
            List<ScreenSummaryResources> busSystemList = new ArrayList<>();
            busSystemRiskSortChart.getXAxisList().forEach(busSystemRisk -> {
                ScreenSummaryResources screenSummaryResources = new ScreenSummaryResources();
                screenSummaryResources.setType(SummaryTypeEnum.RISK_MODEL.getType());
                screenSummaryResources.setName(modelName);
                screenSummaryResources.setResult(judgeValue(busSystemRisk.getConfigIndicatorValue(), comprehensiveChart.getDataScopeList()));
                screenSummaryResources.setSystemId(Math.toIntExact(busSystemMap.get(busSystemRisk.getConfigName())));
                screenSummaryResources.setCreateTime(LocalDateTime.now());
                busSystemList.add(screenSummaryResources);
            });
            this.saveBatch(busSystemList);
        }
    }

    @SchemaSwitch(String.class)
    @Override
    public void updateItemAbilityScreenStatistics(String operationId) {
        //获取该业务系统下对应核查项能力检测结果和数源对应数据库检测结果
        //1.获取生命周期标签
        Map<Long, String> tagMap = tagMapper.selectList(new QueryWrapper<Tag>().eq("type_id", 6)).stream()
            .collect(Collectors.toMap(Tag::getId, Tag::getName));
        //2.获取生命标签下的所有问题
        List<ItemAbilityDTO> list = questionnaireService.selectItemAbilityWithLifecycleTag(operationId);

        //3.获取所有数源id-name映射
        List<PreSourceConfig> preSourceConfigList = preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", "-1"));
        Map<String, Integer> sourceIdMap = preSourceConfigList.stream()
            .collect(Collectors.toMap(PreSourceConfig::getConfigName, PreSourceConfig::getId));

        List<ScreenSummaryResources> result = new ArrayList<>();
        List<Long> systemIds = new ArrayList<>();
        Set<Integer> currentSourceIds = new HashSet<Integer>();
        if (CollUtil.isNotEmpty(list)) {
            list.stream().filter(itemAbilityDTO -> StrUtil.isNotEmpty(itemAbilityDTO.getAbility()))
                .collect(Collectors.groupingBy(ItemAbilityDTO::getObjectId)).forEach((k, v) -> {
                    List<LifeCycleAbilityDTO> lifeCycleAbilityList = new ArrayList<>();
                    // 根据生命周期标签分组
                    for (ItemAbilityDTO itemAbilityDTO : v) {
                        Arrays.asList(itemAbilityDTO.getLifecycleIds().split(StrUtil.COMMA)).forEach(lifeCycle -> {
                            Arrays.asList(itemAbilityDTO.getAbility().split(StrUtil.COMMA)).forEach(ability -> {
                                LifeCycleAbilityDTO lifeCycleAbility = new LifeCycleAbilityDTO();
                                lifeCycleAbility.setLifecycleId(lifeCycle);
                                lifeCycleAbility.setAbility(ability);
                                lifeCycleAbilityList.add(lifeCycleAbility);
                            });
                        });
                    }
                    lifeCycleAbilityList.stream().collect(Collectors.groupingBy(LifeCycleAbilityDTO::getLifecycleId)).forEach((k1, v1)->{
                        AbilityItemReq req = new AbilityItemReq();
                        req.setOperationId(operationId);
                        req.setObjectId(k);
                        req.setAbilityIds(v1.stream().map(LifeCycleAbilityDTO::getAbility).collect(Collectors.joining(StrUtil.COMMA)));
                        ItemAbilityVO abilityVO = questionnaireService.queryAbilityItem(req);
                        ScreenSummaryResources screenSummaryResources = new ScreenSummaryResources();
                        screenSummaryResources.setType(SummaryTypeEnum.ITEM_ABILITY.getType());
                        screenSummaryResources.setName(tagMap.get(Long.parseLong(k1)));
                        screenSummaryResources.setResult(JSONUtil.toJsonStr(abilityVO));
                        screenSummaryResources.setCreateTime(LocalDateTime.now());
                        screenSummaryResources.setSystemId(Math.toIntExact(k));
                        result.add(screenSummaryResources);

                        // 细分到数源维度
                        Map<String, List<ItemAbilitySummaryVO>> sourceNameAbilityMap = processAbilityCount(abilityVO);
                        sourceNameAbilityMap.forEach((name, itemAbilityList) ->{
                            ScreenSummaryResources copy = new ScreenSummaryResources();
                            BeanUtils.copyProperties(screenSummaryResources, copy);
                            copy.setType(SummaryTypeEnum.ITEM_ABILITY_COUNT.getType());
                            copy.setSourceId(sourceIdMap.get(name));
                            currentSourceIds.add(copy.getSourceId());
                            copy.setResult(JSONUtil.toJsonStr(itemAbilityList));
                            result.add(copy);
                        });
                    });
                    systemIds.add(k);
                });
            if (CollUtil.isNotEmpty(systemIds)) {
                this.remove(new QueryWrapper<ScreenSummaryResources>().in("system_id",systemIds).eq("type", 4));
                if (CollUtil.isNotEmpty(currentSourceIds))
                    this.remove(new QueryWrapper<ScreenSummaryResources>().in("system_id", systemIds).eq("type", 5)
                            .in("source_id", currentSourceIds));
            }
            this.saveBatch(result);
        }
    }

    private Map<String, List<ItemAbilitySummaryVO>> processAbilityCount(ItemAbilityVO abilityVO) {
        List<ItemAbilitySummaryVO> list = new ArrayList<>();
        Map<String, List<ItemAbilityVO.Config>> techItemMap =
            abilityVO.getTechList().stream().collect(Collectors.groupingBy(ItemAbilityVO.Config::getName));
        abilityVO.getDbList().stream().collect(Collectors.groupingBy(ItemAbilityVO.Config::getName)).forEach((k,v) -> {
            ItemAbilitySummaryVO vo = new ItemAbilitySummaryVO();
            ItemAbilitySummaryVO.AbilitySummary dbSummary = new ItemAbilitySummaryVO.AbilitySummary();
            v.forEach(config -> {
                Map<String, List<ItemAbilityVO.Item>> itemCountMap =
                    config.getItems().stream().collect(Collectors.groupingBy(ItemAbilityVO.Item::getResult));
                dbSummary.setCountA(itemCountMap.containsKey("具备")? itemCountMap.get("具备").size() : 0);
                dbSummary.setCountB(itemCountMap.containsKey("部分具备")? itemCountMap.get("部分具备").size() : 0);
                dbSummary.setCountC(itemCountMap.containsKey("不具备")? itemCountMap.get("不具备").size() : 0);
            });
            vo.setName(k);
            vo.setDbSummary(dbSummary);
            if (techItemMap.containsKey(k)) {
                List<ItemAbilityVO.Config> abilityList = techItemMap.get(k);
                ItemAbilitySummaryVO.AbilitySummary abilitySummary = new ItemAbilitySummaryVO.AbilitySummary();
                abilityList.forEach(ability -> {
                    Map<String, List<ItemAbilityVO.Item>> itemCountMap =
                        ability.getItems().stream().collect(Collectors.groupingBy(ItemAbilityVO.Item::getResult));
                    abilitySummary.setCountA(itemCountMap.containsKey("具备")? itemCountMap.get("具备").size() : 0);
                    abilitySummary.setCountB(itemCountMap.containsKey("部分具备")? itemCountMap.get("部分具备").size() : 0);
                    abilitySummary.setCountC(itemCountMap.containsKey("不具备")? itemCountMap.get("不具备").size() : 0);
                });
                vo.setTecSummary(abilitySummary);
            } else {
                vo.setTecSummary(new ItemAbilitySummaryVO.AbilitySummary());
            }
            list.add(vo);
        });
        return  list.stream().collect(Collectors.groupingBy(ItemAbilitySummaryVO::getName));
    }

    private String judgeValue(Object configIndicatorValue, List<ReportConfigDataScopeDTO> dataScopeList) {
        double value = 0d;
        if (configIndicatorValue instanceof Double){
            value = (double)configIndicatorValue;
        } else if (configIndicatorValue instanceof String) {
            value = Double.parseDouble(String.valueOf(configIndicatorValue));
        }
        for (ReportConfigDataScopeDTO dataScopeDTO : dataScopeList) {
            if (dataScopeDTO.getStartScope() <= value && value <= dataScopeDTO.getEndScope()) {
                return dataScopeDTO.getDisplayName();
            }
        }
        return String.valueOf(value);
    }
}
