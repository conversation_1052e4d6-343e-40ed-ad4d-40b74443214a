package com.dcas.system.service;

import cn.hutool.core.lang.Pair;
import com.dcas.common.model.dto.*;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.vo.SystemDetailVO;
import com.dcas.common.model.vo.SystemSourceVO;
import com.dcas.common.utils.PageResult;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/8 17:09
 * @since 1.0.0
 */
public interface CoSystemService {
    void add(SystemAddDTO dto);

    void update(SystemEditDTO dto);

    PageResult<SystemDetailVO> pageQuery(SystemQueryDTO dto);

    void delete(IdsReq ids);

    void addSource(SystemSourceAdd req);

    void deleteSource(SystemSourceAdd req);

    List<SystemSourceVO> getSource(Integer systemId);

    Pair<Integer, String> listKeyValue(String operationId);

    List<TreeLabelDTO> listByOperationId(String operationId);
}
