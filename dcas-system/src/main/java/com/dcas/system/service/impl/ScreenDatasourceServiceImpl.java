package com.dcas.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.core.param.DataSourceParam;
import com.dcas.common.domain.entity.CoSystem;
import com.dcas.common.domain.entity.PreSourceConfig;
import com.dcas.common.domain.entity.ScreenDatasource;
import com.dcas.common.enums.DataSourceType;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.mapper.CoSystemMapper;
import com.dcas.common.mapper.PreSourceConfigMapper;
import com.dcas.common.mapper.ScreenDatasourceMapper;
import com.dcas.common.model.dto.ScreenDatasourceDTO;
import com.dcas.common.model.vo.ScreenDatasourceVO;
import com.dcas.common.model.vo.SourceConfigVO;
import com.dcas.common.utils.Func;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.discovery.factory.DatabaseFactory;
import com.dcas.system.domain.resp.TestConnectionResponse;
import com.dcas.system.service.IScreenDatasourceService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.mchz.mcdatasource.core.DataBaseType;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 大屏数源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RequiredArgsConstructor
@Service
public class ScreenDatasourceServiceImpl extends ServiceImpl<ScreenDatasourceMapper, ScreenDatasource>
    implements IScreenDatasourceService {

    private final ScreenDatasourceMapper screenDatasourceMapper;
    private final PreSourceConfigMapper preSourceConfigMapper;
    private final CoSystemMapper coSystemMapper;
    private final DatabaseFactory databaseFactory;

    @Override
    public PageInfo<ScreenDatasourceVO> page(Integer pageNum, Integer pageSize, String searchValue) {
        //分页
        try (Page<Object> ignored = PageHelper.startPage(pageNum, pageSize)) {
            //查询结果
            List<ScreenDatasourceDTO> list = screenDatasourceMapper.queryBusSystemList(searchValue);
            List<PreSourceConfig> sourceConfigs =
                preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().isNotNull("system_id"));
            Map<Long, List<PreSourceConfig>> map =
                sourceConfigs.stream().collect(Collectors.groupingBy(PreSourceConfig::getSystemId));
            List<ScreenDatasourceVO> result = new ArrayList<>();
            list.stream().collect(Collectors.groupingBy(ScreenDatasourceDTO::getSystemId)).forEach((k, v) -> {
                long systemId = k;
                ScreenDatasourceDTO dto = v.stream().findFirst().get();
                List<Integer> sourceIds = v.stream().map(ScreenDatasourceDTO::getSourceId).collect(Collectors.toList());
                ScreenDatasourceVO vo = new ScreenDatasourceVO();
                BeanUtils.copyProperties(dto, vo);
                if (map.containsKey(systemId)) {
                    List<PreSourceConfig> preSourceConfigList = map.get(systemId);
                    List<SourceConfigVO> sourceConfigList = preSourceConfigList.stream()
                        .filter(preSourceConfig -> sourceIds.contains(preSourceConfig.getId())).map(preSourceConfig -> {
                            SourceConfigVO sourceConfigVO = new SourceConfigVO();
                            BeanUtils.copyProperties(preSourceConfig, sourceConfigVO);
                            sourceConfigVO.setStatus(dto.getStatus());
                            return sourceConfigVO;
                        }).distinct().collect(Collectors.toList());
                    vo.setSourceConfigList(sourceConfigList);
                }
                result.add(vo);
            });
            return new PageInfo<>(result);
        }
    }

    @Override
    public void saveScreenDatasource(ScreenDatasourceDTO dto) {
        List<PreSourceConfig> systemSourceList = preSourceConfigMapper.selectList(
                new QueryWrapper<PreSourceConfig>().eq("operation_id", "-1").in("system_id", dto.getSystemId())).stream()
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(systemSourceList)) {
            throw new ServiceException("该业务系统未关联数源，无法添加，请前往业务系统管理页面添加！");
        }
        if (screenDatasourceMapper.selectCount(new QueryWrapper<ScreenDatasource>().eq("system_id",dto.getSystemId())) > 0){
            throw new ServiceException("该业务系统已存在数源，请勿重复添加！");
        }
        systemSourceList.forEach(preSourceConfig -> {
            ScreenDatasource screenDatasource = new ScreenDatasource();
            BeanUtils.copyProperties(dto, screenDatasource);
            screenDatasource.setSourceId(preSourceConfig.getId());
            screenDatasource.setDelFlag(false);
            screenDatasource.setCreateTime(LocalDateTime.now());
            screenDatasource.setUpdateTime(LocalDateTime.now());
            screenDatasource.setCreateBy(SecurityUtils.getAccount());
            screenDatasource.setUpdateBy(SecurityUtils.getAccount());
            screenDatasourceMapper.insert(screenDatasource);
        });
    }

    @Override
    public void updateScreenDatasource(ScreenDatasourceDTO dto) {

    }

    @Override
    public void removeBySystemIds(Integer[] systemIds) {
        screenDatasourceMapper.delete(new QueryWrapper<ScreenDatasource>().in("system_id", Arrays.asList(systemIds)));
    }

    @Override
    public TestConnectionResponse testConnect(Integer id) {
        PreSourceConfig preSourceConfig = preSourceConfigMapper.selectById(id);
        DataSourceParam param = new DataSourceParam();
        TestConnectionResponse response = new TestConnectionResponse();
        response.setSuccess(true);
        response.setMessage("连接成功!");
        ScreenDatasourceDTO dto = new ScreenDatasourceDTO();
        dto.setSourceId(id);
        try {
            BeanUtils.copyProperties(preSourceConfig, param);
            param.setPassword(SecurityUtils.decryptAes(preSourceConfig.getPassword()));
            param.setDataBaseType(DataSourceType.getType(preSourceConfig.getConfigType()).getDataBaseType());
            databaseFactory.testConnection(param);
            // 更新数源状态
            dto.setStatus(1);
            screenDatasourceMapper.updateStatus(dto);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage("无法连接，请尝试在数源管理中重新连接。");
            // 更新数源状态
            dto.setStatus(0);
            screenDatasourceMapper.updateStatus(dto);
        }
        return response;
    }

    @Override
    public TestConnectionResponse testConnectBySystemId(Integer systemId) {
        List<PreSourceConfig> sourceConfigs = preSourceConfigMapper.selectList(
            new QueryWrapper<PreSourceConfig>().eq("operation_id", "-1").eq("system_id", systemId));
        TestConnectionResponse response = new TestConnectionResponse();
        response.setSuccess(true);
        response.setMessage("连接成功!");
        if (CollUtil.isEmpty(sourceConfigs)) {
            response.setSuccess(false);
            response.setMessage("该系统无可测试数据源");
            return response;
        }
        ScreenDatasourceDTO dto = new ScreenDatasourceDTO();
        StringBuilder sb = new StringBuilder();
        sourceConfigs.forEach(preSourceConfig -> {
            TestConnectionResponse res = testConnect(preSourceConfig.getId());
            if (!res.isSuccess()) {
                sb.append(preSourceConfig.getConfigName()).append("、");
                // 更新数源状态
                dto.setSourceId(preSourceConfig.getId());
                dto.setStatus(0);
                screenDatasourceMapper.updateStatus(dto);
            } else {
                // 更新数源状态
                dto.setSourceId(preSourceConfig.getId());
                dto.setStatus(1);
                screenDatasourceMapper.updateStatus(dto);
            }
        });
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.lastIndexOf("、"));
            response.setSuccess(false);
            response.setMessage(CharSequenceUtil.concat(true, sb, "数源连接失败，请前往数源管理重新连接数源详情"));
        }
        return response;
    }

    @Override
    public List<ScreenDatasourceVO> getBusSystemList() {
        List<CoSystem> systemList = coSystemMapper.selectList(null);
        List<PreSourceConfig> sourceConfigs =
            preSourceConfigMapper.selectList(new QueryWrapper<PreSourceConfig>().eq("operation_id", "-1"));
        List<ScreenDatasourceVO> list = new ArrayList<>();
        if (CollUtil.isEmpty(systemList)) {
            return list;
        }
        Map<Long, List<PreSourceConfig>> map = new HashMap<>(16);
        if (CollUtil.isNotEmpty(sourceConfigs)) {
            map = sourceConfigs.stream().collect(Collectors.groupingBy(PreSourceConfig::getSystemId));
        }
        Map<Long, List<PreSourceConfig>> finalMap = map;
        systemList.forEach(coSystem -> {
            long systemId = coSystem.getId();
            if (!finalMap.containsKey(systemId)) {
                return;
            }
            List<PreSourceConfig> preSourceConfigList = finalMap.get(systemId);
            ScreenDatasourceVO vo = new ScreenDatasourceVO();
            vo.setSystemId(coSystem.getId());
            vo.setBusSystem(coSystem.getName());
            vo.setIntro(coSystem.getIntroduce());
            vo.setSourceConfigList(preSourceConfigList.stream().map(preSourceConfig -> {
                SourceConfigVO sourceConfigVO = new SourceConfigVO();
                BeanUtils.copyProperties(preSourceConfig, sourceConfigVO);
                return sourceConfigVO;
            }).collect(Collectors.toList()));
            list.add(vo);
        });
        return list;
    }
}
