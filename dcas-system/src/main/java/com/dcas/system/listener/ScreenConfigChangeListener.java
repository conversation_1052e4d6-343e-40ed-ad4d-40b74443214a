package com.dcas.system.listener;

import com.dcas.common.domain.entity.ScreenConfig;
import com.dcas.system.manager.ScreenTaskScheduler;
import com.dcas.system.spring.event.ScreenConfigChangeEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/10 15:11
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ScreenConfigChangeListener {
    private final ScreenTaskScheduler taskScheduler;

    @EventListener
    public void onConfigChange(ScreenConfigChangeEvent event) {
        try {
            ScreenConfig config = event.getScreenConfig();
            ScreenConfigChangeEvent.ChangeType changeType = event.getChangeType();

            log.info("接收到大屏配置变更事件, ID: {}, 变更类型: {}", config.getId(), changeType);

            switch (changeType) {
                case CREATE:
                case UPDATE:
                    if (config.getDataUpdateType() == 1) {
                        taskScheduler.scheduleTask(config);
                    }
                    break;
                case DELETE:
                    taskScheduler.cancelTask(config.getId());
                    break;
            }
        } catch (Exception e) {
            log.error("处理配置变更事件失败", e);
        }
    }
}
