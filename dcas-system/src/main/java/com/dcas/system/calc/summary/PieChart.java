package com.dcas.system.calc.summary;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.enums.OperationEnum;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.system.calc.IndicatorCalcUtil;
import com.dcas.system.calc.operator.OperatorFactory;
import com.dcas.common.model.dto.ReportConfigBaseDTO;
import com.dcas.common.model.dto.ReportConfigDetailDTO;
import com.dcas.common.domain.entity.IndicatorConditionConfig;
import com.dcas.common.domain.entity.IndicatorConfig;
import com.dcas.common.domain.entity.IndicatorResult;
import com.dcas.common.domain.entity.ReportConfig;
import com.dcas.common.model.param.CalcParam;
import com.dcas.common.model.vo.RiskAnalysisReportVO;
import com.dcas.common.mapper.IndicatorConditionConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 业务系统-饼图
 *
 * <AUTHOR>
 * @date 2024/03/04 10:41
 **/
public class PieChart implements ChartInterface {

    private final static Logger logger = LoggerFactory.getLogger("risk-calc");

    @Override
    public void getChartData(ReportConfig reportConfig, CalcParam calcParam,
        RiskAnalysisReportVO riskAnalysisReportVO) {
        if (!reportConfig.getEnable()) {
            return;
        }
        ReportConfigDetailDTO detail = JSONUtil.toBean(reportConfig.getConfig(), ReportConfigDetailDTO.class);
        Map<String, List<IndicatorResult>> busSystemIndicatorResultMap = calcParam.getBusSystemIndicatorResultMap();
        if (CollUtil.isEmpty(riskAnalysisReportVO.getBusSystemRiskDetailChartList())) {
            List<RiskAnalysisReportVO.BusSystemRiskDetailChart> list = new ArrayList<>();
            for (Map.Entry<String, List<IndicatorResult>> entry : busSystemIndicatorResultMap.entrySet()) {
                RiskAnalysisReportVO.BusSystemRiskDetailChart busSystemRiskDetailChart =
                    new RiskAnalysisReportVO.BusSystemRiskDetailChart();
                // 业务系统—饼状图
                RiskAnalysisReportVO.PieChart pieChart = new RiskAnalysisReportVO.PieChart();
                pieChart.setDisplayName(reportConfig.getName());
                pieChart.setBusSystemName(entry.getKey());
                pieChart
                    .setPieList(doCalcIndicator(calcParam, detail, entry.getKey(), entry.getValue()));
                busSystemRiskDetailChart.setPieChart(pieChart);
                list.add(busSystemRiskDetailChart);
            }
            riskAnalysisReportVO.setBusSystemRiskDetailChartList(list);
        } else {
            for (RiskAnalysisReportVO.BusSystemRiskDetailChart busSystemRiskDetailChart : riskAnalysisReportVO
                .getBusSystemRiskDetailChartList()) {
                RiskAnalysisReportVO.PieChart pieChart = new RiskAnalysisReportVO.PieChart();
                String busSystem = busSystemRiskDetailChart.getHistogramChart().getBusSystemName();
                if ("汇总分析".equals(busSystem)){
                    continue;
                }
                List<IndicatorResult> indicatorResultList = busSystemIndicatorResultMap.get(busSystem);
                pieChart.setDisplayName(reportConfig.getName());
                pieChart.setBusSystemName(busSystem);
                pieChart.setPieList(
                    doCalcIndicator(calcParam, detail, busSystem, indicatorResultList));
                busSystemRiskDetailChart.setPieChart(pieChart);
            }
        }

        // 添加汇总分析
        addSummary(riskAnalysisReportVO.getBusSystemRiskDetailChartList());
    }


    private void addSummary(List<RiskAnalysisReportVO.BusSystemRiskDetailChart> busSystemRiskDetailChartList) {
        if (busSystemRiskDetailChartList.size() > 1){
            List<RiskAnalysisReportVO.PieChart> pieCharts = busSystemRiskDetailChartList.stream()
                .map(RiskAnalysisReportVO.BusSystemRiskDetailChart::getPieChart)
                .filter(Objects::nonNull).collect(Collectors.toList());
            boolean exist = false;
            for (RiskAnalysisReportVO.BusSystemRiskDetailChart busSystemRiskDetailChart : busSystemRiskDetailChartList){
                if (busSystemRiskDetailChart != null && busSystemRiskDetailChart.getHistogramChart() != null && "汇总分析".equals(
                    busSystemRiskDetailChart.getHistogramChart().getBusSystemName())) {
                    busSystemRiskDetailChart.setPieChart(addSummaryPie(pieCharts));
                    exist = true;
                    break;
                }
            }
            if (!exist){
                RiskAnalysisReportVO.BusSystemRiskDetailChart busSystemRiskDetailChart = new RiskAnalysisReportVO.BusSystemRiskDetailChart();
                RiskAnalysisReportVO.PieChart pieChart = addSummaryPie(pieCharts);
                busSystemRiskDetailChart.setPieChart(pieChart);
                busSystemRiskDetailChartList.add(busSystemRiskDetailChart);
            }
        }
    }

    private RiskAnalysisReportVO.PieChart addSummaryPie(List<RiskAnalysisReportVO.PieChart> pieCharts) {
        List<ReportConfigBaseDTO> list = new ArrayList<>();
        RiskAnalysisReportVO.PieChart summaryPieChart = new RiskAnalysisReportVO.PieChart();
        List<ReportConfigBaseDTO> allList = new ArrayList<>();

        pieCharts.forEach(pieChart -> {
            allList.addAll(pieChart.getPieList());
            summaryPieChart.setDisplayName(pieChart.getDisplayName());
        });
        // 保留两位小数
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        allList.stream().collect(Collectors.groupingBy(ReportConfigBaseDTO::getConfigName)).forEach((k, v) -> {
            ReportConfigBaseDTO reportConfigBaseDTO = new ReportConfigBaseDTO();
            reportConfigBaseDTO.setConfigName(k);
            BeanUtils.copyProperties(v.get(0), reportConfigBaseDTO);
            reportConfigBaseDTO.setConfigIndicatorValue(numberFormat.format(
                v.stream().mapToDouble(s -> Double.parseDouble(String.valueOf(s.getConfigIndicatorValue()))).average()
                    .orElse(0)));
            list.add(reportConfigBaseDTO);
        });
        RiskAnalysisReportVO.PieChart pieChart = pieCharts.get(0);
        List<String> sortList = new ArrayList<>();
        pieChart.getPieList().forEach(p -> sortList.add(p.getConfigName()));
        summaryPieChart.setPieList(list.stream().sorted(Comparator.comparing(s->sortList.indexOf(s.getConfigName()))).collect(
            Collectors.toList()));
        summaryPieChart.setBusSystemName("汇总分析");
        return summaryPieChart;
    }

    private List<ReportConfigBaseDTO> doCalcIndicator(CalcParam calcParam, ReportConfigDetailDTO detail,
        String busSystem, List<IndicatorResult> indicatorResultList) {
        Map<String, List<Long>> sortMap = new HashMap<>(16);
        IndicatorConditionConfigMapper indicatorConditionConfigMapper = SpringUtils.getBean(IndicatorConditionConfigMapper.class);
        Map<String, Object> stepResultMap = calcParam.getIndicatorResultMap();
        Map<Long, IndicatorConfig> indicatorConfigMap = calcParam.getIndicatorConfigMap();
        List<ReportConfigBaseDTO> list = new ArrayList<>();
        for (ReportConfigBaseDTO dto : detail.getBaseList()){
            ReportConfigBaseDTO target = new ReportConfigBaseDTO();
            BeanUtils.copyProperties(dto, target);

            IndicatorCalcUtil.sortIndicatorDependency(dto.getConfigName(), dto.getConfigIndicator(),
                calcParam.getIndicatorConfigMap(), sortMap, calcParam.getCalIndicatorList());
            List<Long> processList = sortMap.get(dto.getConfigName());

            if (CollUtil.isEmpty(processList)) {
                processList = new ArrayList<>();
                processList.add(dto.getConfigIndicator());
            }

            // 查询所有指标配置条件
            List<IndicatorConditionConfig> conditionConfigs = indicatorConditionConfigMapper
                .selectList(new QueryWrapper<IndicatorConditionConfig>().in("indicator_id", processList));

            Map<Long, List<IndicatorConditionConfig>> conditionConfigMap =
                conditionConfigs.stream().collect(Collectors.groupingBy(IndicatorConditionConfig::getIndicatorId));
            Collections.reverse(processList);
            logger.info("饼图{}需处理的指标：{}", dto.getConfigName(), JSONUtil.toJsonStr(processList));

            for (Long indicatorId : processList){
                String key = String.join("_", String.valueOf(indicatorId), busSystem);
                if (stepResultMap.containsKey(key)){
                    continue;
                }
                IndicatorConfig indicatorConfig = indicatorConfigMap.get(indicatorId);
                List<IndicatorConditionConfig> indicatorConditionConfigs = conditionConfigMap.get(indicatorId);
                calc(indicatorConfig, indicatorConditionConfigs, indicatorResultList, busSystem, stepResultMap);
            }
            target.setConfigIndicatorValue(stepResultMap.get(String.join("_", String.valueOf(dto.getConfigIndicator()), busSystem)));
            list.add(target);
        }

        return list;
    }

    private void calc(IndicatorConfig indicatorConfig, List<IndicatorConditionConfig> indicatorConditionConfigs,
        List<IndicatorResult> indicatorResultList, String busSystem, Map<String, Object> stepResultMap) {
        logger.info("计算饼图统计指标：{}-{}", indicatorConfig.getId(),indicatorConfig.getEnName());
        // 处理范围规则
        final Pattern pattern = Pattern.compile(IndicatorCalcUtil.REGX);
        final Matcher matcher = pattern.matcher(indicatorConfig.getFormula());
        Object obj = null;

        List<Object> resList = new ArrayList<>();
        while (matcher.find()){
            final String group = matcher.group();
            String indicatorId = group.substring(2, group.length() - 1);
            String key = String.join("_", indicatorId, busSystem);
            obj = stepResultMap.get(key) == null ? stepResultMap.get(indicatorId) : stepResultMap.get(key);
            if (obj == null){
                for (IndicatorResult indicatorResult : indicatorResultList){
                    Map<String, Object> map = JSONUtil.toBean(indicatorResult.getResult(), Map.class);
                    resList.add(map.get(indicatorId));
                }
            } else {
                if (obj instanceof ArrayList){
                    resList = (List<Object>)obj;
                } else {
                    resList.add(obj);
                }
            }
        }
        BigDecimal result = BigDecimal.ZERO;
        if (indicatorConfig.getFormula().startsWith(OperationEnum.AVG.getValue())){
            result = OperatorFactory
                .getOperatorByType(OperationEnum.AVG.getType()).process(resList, stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.SUM.getValue())){
            result = OperatorFactory.getOperatorByType(OperationEnum.SUM.getType()).process(resList, stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.COUNT.getValue())){
            result = OperatorFactory.getOperatorByType(OperationEnum.COUNT.getType()).process(resList,stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.MAX.getValue())){
            result = OperatorFactory.getOperatorByType(OperationEnum.MAX.getType()).process(resList, stepResultMap);
        }
        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMaximumFractionDigits(2);
        String key = String.join("_", String.valueOf(indicatorConfig.getId()), busSystem);
        stepResultMap.put(key, numberFormat.format(result));
        logger.info("计算饼图统计指标：{}-{}结束，结果为:{}", indicatorConfig.getId(),indicatorConfig.getEnName(),result);
    }
}
