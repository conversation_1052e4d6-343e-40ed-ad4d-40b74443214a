package com.dcas.system.calc.operator;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.dcas.common.enums.OperationEnum;
import com.dcas.common.enums.OptEnum;
import com.dcas.common.exception.ServiceException;
import com.dcas.system.calc.Compute;
import com.dcas.system.calc.IndicatorCalcUtil;
import com.dcas.system.calc.rule.ConditionTranslatorInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.dcas.system.calc.Compute.PATTERN_INNER;
import static com.dcas.system.calc.Compute.calculate;

/**
 * 逻辑判断操作
 * <AUTHOR>
 * @date 2024/01/16 16:39
 **/
public class IfsOperator extends AbstractOperator implements OperatorInterface{

    private final static Logger logger = LoggerFactory.getLogger("risk-calc");
    private final Pattern pattern = Pattern.compile(IndicatorCalcUtil.REGX);

    @Override
    public Long getType() {
        return OperationEnum.IFS.getType();
    }

    @Override
    public BigDecimal process(Object value, Map<String, Object> stepResultMap,
        ConditionTranslatorInterface conditionTranslatorInterface) throws ServiceException {
        logger.warn("not impl!");
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal process(Object value, Map<String, Object> stepResultMap) throws ServiceException{

        String formula = value.toString();
        logger.info("formula:{}", formula);
        try {
            if (formula.startsWith(OperationEnum.IFS.getValue())) {
                formula = formula.substring(4, formula.length() - 1);
            }
            if (PATTERN_INNER.matcher(formula).find()) {
                BigDecimal obj = calculate(formula, stepResultMap);
                formula = obj.toString();
            }
            String[] exprArr = formula.split(OperationEnum.COMM.getValue());
            List<String> conditions = new ArrayList<>();
            List<String> results = new ArrayList<>();
            int i = 1;
            // IFS("111",5,"222",4)、IFS("true",5,4)
            // 将条件和结果分组,分情况：偶数、基数
            if (exprArr.length % 2 == 0) {
                // 偶数长度表达式：偶数位为结果，其余为条件
                for (String temp : exprArr) {
                    if (i % 2 == 0) {
                        String result = getResult(temp, stepResultMap);
                        results.add(result);
                    } else {
                        conditions.add(temp);
                    }
                    i++;
                }
            } else {
                // 基数长度表达式：除偶数位和最后一位为结果，其余为条件
                for (String temp : exprArr) {
                    if (i % 2 == 0) {
                        String result = getResult(temp, stepResultMap);
                        results.add(result);
                    } else if (i == exprArr.length) {
                        String result = getResult(temp, stepResultMap);
                        results.add(result);
                    } else {
                        conditions.add(temp);
                    }
                    i++;
                }
            }

            BigDecimal result = BigDecimal.valueOf(Double.parseDouble(results.get(results.size() - 1)));
            for (int j = 0; j < conditions.size(); j++) {
                String temp = conditions.get(j);
                List<String> conditionList = new ArrayList<>();
                if (temp.contains(OperationEnum.AND.getValue())) {
                    String[] andArr = temp.split(OperationEnum.AND.getValue());
                    for (String s : andArr) {
                        conditionList.add(getConditionResult(s, stepResultMap));
                    }
                } else if (temp.contains(OperationEnum.OR.getValue())) {
                    String[] orArr = temp.split(OperationEnum.OR.getValue());
                    for (String s : orArr) {
                        conditionList.add(getConditionResult(s, stepResultMap));
                    }
                } else {
                    conditionList.add(getConditionResult(temp, stepResultMap));
                }
                boolean allTrue = conditionList.stream().allMatch("true"::equals);
                if (allTrue) {
                    return BigDecimal.valueOf(Double.parseDouble(results.get(j)));
                }
            }
            return result;
        } catch (Exception e){
            throw new ServiceException("formula 【"+formula+"】 calculation failed!", e);
        }
    }

    /**
     * 1、true or false
     * 2、1 == 1
     * 3、1
     * @param s
     * @param stepResultMap
     * @return
     */
    private String getConditionResult(String s, Map<String, Object> stepResultMap) {
        String obj = null;
        String result = null;
        String[] conditionArr;
        if (s.contains(OperationEnum.EQ.getValue())){
            conditionArr = s.split(OperationEnum.EQ.getValue());
            obj = getIndicatorResult(conditionArr[0]);
            result = getIndicatorResult(conditionArr[1]);
            if (BigDecimal.valueOf(Double.parseDouble(obj)).compareTo(BigDecimal.valueOf(Double.parseDouble(result))) == 0){
                obj = "true";
            } else {
                obj = "false";
            }
        } else if (s.contains(OperationEnum.LE.getValue())) {
            conditionArr = s.split(OperationEnum.LE.getValue());
            obj = getIndicatorResult(conditionArr[0]);
            result = getIndicatorResult(conditionArr[1]);
            if (BigDecimal.valueOf(Double.parseDouble(obj)).compareTo(BigDecimal.valueOf(Double.parseDouble(result))) <= 0){
                obj = "true";
            } else {
                obj = "false";
            }
        } else if (s.contains(OperationEnum.GE.getValue())) {
            conditionArr = s.split(OperationEnum.GE.getValue());
            obj = getIndicatorResult(conditionArr[0]);
            result = getIndicatorResult(conditionArr[1]);
            if (BigDecimal.valueOf(Double.parseDouble(obj)).compareTo(BigDecimal.valueOf(Double.parseDouble(result))) >= 0){
                obj = "true";
            } else {
                obj = "false";
            }
        }  else if (s.contains(OperationEnum.GT.getValue())) {
            conditionArr = s.split(OperationEnum.GT.getValue());
            obj = getIndicatorResult(conditionArr[0]);
            result = getIndicatorResult(conditionArr[1]);
            if (BigDecimal.valueOf(Double.parseDouble(obj)).compareTo(BigDecimal.valueOf(Double.parseDouble(result))) > 0){
                obj = "true";
            } else {
                obj = "false";
            }
        }  else if (s.contains(OperationEnum.LT.getValue())) {
            conditionArr = s.split(OperationEnum.LT.getValue());
            obj = getIndicatorResult(conditionArr[0]);
            result = getIndicatorResult(conditionArr[1]);
            if (BigDecimal.valueOf(Double.parseDouble(obj)).compareTo(BigDecimal.valueOf(Double.parseDouble(result))) < 0){
                obj = "true";
            } else {
                obj = "false";
            }
        } else {
            obj = getIndicatorResult(s);
            if ("0".equals(obj) || CharSequenceUtil.isEmpty(obj)) {
                obj = "false";
            } else {
                obj = "true";
            }
        }
        return obj;
    }

    private String getIndicatorResult(String indicatorExpr) {
        String obj = "";
        if (indicatorExpr.startsWith(OperationEnum.COUNT.getValue())) {
            String s = indicatorExpr.substring(3, indicatorExpr.length()-1);
            if (JSONUtil.isTypeJSONArray(s)){
                List<String> list = JSONUtil.toList(s, String.class);
                obj = String.valueOf(list.size());
            }
        } else  if (indicatorExpr.contains(OperationEnum.ADD.getValue()) || indicatorExpr.contains(OperationEnum.SUBTRACT.getValue()) || indicatorExpr.contains(
            OperationEnum.DIVIDE.getValue()) || indicatorExpr.contains(OperationEnum.MULTIPLY.getValue())) {
            try {
                obj = Compute.getResult(indicatorExpr);
            } catch (Exception e){
                logger.error("{} calculation failed!", indicatorExpr);
            }
        } else {
            if (JSONUtil.isTypeJSONArray(indicatorExpr)){
                List<String> list =  JSONUtil.toList(indicatorExpr, String.class);
                if (!list.isEmpty()){
                    obj = list.get(0);
                }
            } else {
                obj = indicatorExpr;
            }
        }
        logger.info("indicator expr :{}, indicator result:{}", indicatorExpr, obj);
        return obj;
    }

    private String getResult(String s, Map<String, Object> stepResultMap) throws Exception {
        final Matcher matcher = pattern.matcher(s);
        String obj = s;
        if (matcher.find()) {
            final String group = matcher.group();
            String indicatorId = group.substring(2, group.length() - 1);
            obj = (String)stepResultMap.get(indicatorId);
        }
        if (obj.endsWith(")")){
            obj = obj.substring(0, obj.length()-1);
        }
        if (JSONUtil.isTypeJSONArray(s)){
            List<String> list = JSONUtil.toList(s, String.class);
            if (!list.isEmpty()){
                obj = list.get(0);
            }
        }
        if (s.contains(OperationEnum.ADD.getValue()) || s.contains(OperationEnum.SUBTRACT.getValue()) || s.contains(
            OperationEnum.DIVIDE.getValue()) || s.contains(OperationEnum.MULTIPLY.getValue())) {
            obj = Compute.getResult(s);
        }
        return obj;
    }
}
