package com.dcas.system.calc.summary;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dcas.common.domain.entity.IndicatorResult;
import com.dcas.common.enums.OperationEnum;
import com.dcas.common.utils.spring.SpringUtils;
import com.dcas.system.calc.IndicatorCalcUtil;
import com.dcas.system.calc.operator.OperatorFactory;
import com.dcas.common.model.dto.ReportConfigDetailDTO;
import com.dcas.common.domain.entity.IndicatorConditionConfig;
import com.dcas.common.domain.entity.IndicatorConfig;
import com.dcas.common.domain.entity.ReportConfig;
import com.dcas.common.model.param.CalcParam;
import com.dcas.common.model.vo.RiskAnalysisReportVO;
import com.dcas.common.mapper.IndicatorConditionConfigMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 综合计算值仪表盘图表
 *
 * <AUTHOR>
 * @date 2024/03/04 10:40
 **/
public class ComprehensiveChart implements ChartInterface {
    private final static Logger logger = LoggerFactory.getLogger("risk-calc");

    @Override
    public void getChartData(ReportConfig reportConfig, CalcParam calcParam,
        RiskAnalysisReportVO riskAnalysisReportVO) {
        ReportConfigDetailDTO detail = JSONUtil.toBean(reportConfig.getConfig(), ReportConfigDetailDTO.class);
        IndicatorConditionConfigMapper indicatorConditionConfigMapper =
            SpringUtils.getBean(IndicatorConditionConfigMapper.class);

        // 综合计算值图表
        Map<String, List<Long>> sortMap = new HashMap<>(16);
        IndicatorCalcUtil.sortIndicatorDependency(detail.getConfigName(), detail.getConfigIndicator(),
            calcParam.getIndicatorConfigMap(), sortMap, calcParam.getCalIndicatorList());
        List<Long> processList = sortMap.get(detail.getConfigName());
        if (CollUtil.isEmpty(processList)) {
            processList = new ArrayList<>();
            processList.add(detail.getConfigIndicator());
        }

        // 查询所有指标配置条件
        List<IndicatorConditionConfig> conditionConfigs = indicatorConditionConfigMapper
            .selectList(new QueryWrapper<IndicatorConditionConfig>().in("indicator_id", processList));

        Map<Long, List<IndicatorConditionConfig>> conditionConfigMap =
            conditionConfigs.stream().collect(Collectors.groupingBy(IndicatorConditionConfig::getIndicatorId));
        Collections.reverse(processList);
        logger.info("仪表盘{}需处理的指标：{}", detail.getConfigName(), JSONUtil.toJsonStr(processList));

        calcParam.setConditionConfigMap(conditionConfigMap);
        calcParam.setProcessList(processList);
        RiskAnalysisReportVO.ComprehensiveChart comprehensiveChart = new RiskAnalysisReportVO.ComprehensiveChart();
        comprehensiveChart.setName(detail.getConfigName());
        comprehensiveChart.setDisplayName(reportConfig.getName());
        comprehensiveChart.setDataScopeList(detail.getDataScopeList());
        comprehensiveChart.setValue(doCalcIndicator(calcParam, detail.getConfigIndicator()));
        riskAnalysisReportVO.setComprehensiveChart(comprehensiveChart);
    }

    private String doCalcIndicator(CalcParam calcParam, Long configIndicator) {
        Map<Long, IndicatorConfig> indicatorConfigMap = calcParam.getIndicatorConfigMap();
        Map<String, Object> stepResultMap = calcParam.getIndicatorResultMap();
        Map<Long, List<IndicatorConditionConfig>> conditionConfigMap = calcParam.getConditionConfigMap();
        Map<String, List<IndicatorResult>> busSystemIndicatorResultMap = calcParam.getBusSystemIndicatorResultMap();
        for (Long indicatorId : calcParam.getProcessList()) {
            boolean exist = false;
            for (Map.Entry<String, List<IndicatorResult>> entry : busSystemIndicatorResultMap.entrySet()) {
                String busSystem = entry.getKey();
                String key = String.join("_", String.valueOf(indicatorId), busSystem);
                if (stepResultMap.containsKey(key) || stepResultMap.containsKey(String.valueOf(indicatorId))) {
                    exist = true;
                }
            }
            if (exist){
                continue;
            }
            IndicatorConfig indicatorConfig = indicatorConfigMap.get(indicatorId);
            List<IndicatorConditionConfig> indicatorConditionConfigs = conditionConfigMap.get(indicatorId);
            calc(indicatorConfig, indicatorConditionConfigs, busSystemIndicatorResultMap, stepResultMap);

        }
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMaximumFractionDigits(2);
        if (stepResultMap.containsKey(String.valueOf(configIndicator))){
            double value = Double.parseDouble((String)stepResultMap.get(String.valueOf(configIndicator)));
            return nf.format(value);
        }
        return null;
    }

    private void calc(IndicatorConfig indicatorConfig, List<IndicatorConditionConfig> indicatorConditionConfigs,
        Map<String, List<IndicatorResult>> busSystemIndicatorResultMap, Map<String, Object> stepResultMap) {
        logger.info("计算仪表盘统计指标：{}-{}", indicatorConfig.getId(),indicatorConfig.getEnName());

        // 处理范围规则
        String formula = indicatorConfig.getFormula();
        final Pattern pattern = Pattern.compile(IndicatorCalcUtil.REGX);
        final Matcher matcher = pattern.matcher(formula);
        Object obj = null;

        List<Object> resList = new ArrayList<>();
        while (matcher.find()) {
            final String group = matcher.group();
            String indicatorId = group.substring(2, group.length() - 1);
            for (Map.Entry<String, List<IndicatorResult>> entry: busSystemIndicatorResultMap.entrySet()) {
                String busSystem = entry.getKey();
                String key = String.join("_", indicatorId, busSystem);
                obj = stepResultMap.get(key) == null ? stepResultMap.get(indicatorId) : stepResultMap.get(key);

                if (obj == null){
                    for (IndicatorResult indicatorResult : entry.getValue()){
                        Map<String, Object> map = JSONUtil.toBean(indicatorResult.getResult(), Map.class);
                        resList.add(map.get(indicatorId));
                    }
                } else {
                    if (obj instanceof ArrayList) {
                        resList = (List<Object>)obj;
                    } else {
                        resList.add(obj);
                    }
                    // 替换公式中的变量${}
                    formula = formula.replace(group, String.valueOf(obj));
                }
            }
        }
        BigDecimal result = BigDecimal.ZERO;
        if (indicatorConfig.getFormula().startsWith(OperationEnum.AVG.getValue())) {
            result = OperatorFactory.getOperatorByType(OperationEnum.AVG.getType()).process(resList, stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.SUM.getValue())) {
            result = OperatorFactory.getOperatorByType(OperationEnum.SUM.getType()).process(resList, stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.COUNT.getValue())) {
            result = OperatorFactory.getOperatorByType(OperationEnum.COUNT.getType()).process(resList, stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.MAX.getValue())) {
            result = OperatorFactory.getOperatorByType(OperationEnum.MAX.getType()).process(resList, stepResultMap);
        } else if (indicatorConfig.getFormula().startsWith(OperationEnum.IFS.getValue())) {
            result = OperatorFactory.getOperatorByType(OperationEnum.IFS.getType()).process(formula, stepResultMap);
        }
        NumberFormat numberFormat = DecimalFormat.getNumberInstance();
        stepResultMap.put(String.valueOf(indicatorConfig.getId()), numberFormat.format(result));
        logger.info("计算仪表盘统计指标：{}-{}结束,结果：{}", indicatorConfig.getId(),indicatorConfig.getEnName(), result);
    }
}
