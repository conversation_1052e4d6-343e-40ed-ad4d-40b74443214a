<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.ScreenDatasourceMapper">
    <update id="updateStatus">
        update screen_datasource set status = #{dto.status} where
        <if test="dto.sourceId != null and dto.sourceId != 0">
            source_id = #{dto.sourceId}
        </if>
        <if test="dto.systemId != null and dto.systemId != 0">
            and system_id = #{dto.system}
        </if>
        <if test="dto.id != null and dto.id != 0">
            and id = #{dto.id}
        </if>
    </update>
    <update id="updateDelFlag">
        update screen_datasource set del_flag = true where
        <if test="dto.sourceId != null and dto.sourceId != 0">
            source_id = #{dto.sourceId}
        </if>
        <if test="dto.systemId != null and dto.systemId != 0">
            and system_id = #{dto.system}
        </if>
        <if test="dto.id != null and dto.id != 0">
            and id = #{dto.id}
        </if>
    </update>

    <select id="queryBusSystemList" resultType="com.dcas.common.model.dto.ScreenDatasourceDTO">
        select
        cs.id as systemId,
        cs.name as busSystem,
        cs.introduce as intro,
        sd.source_id as sourceId,
        sd.status
        from
        co_system cs inner join screen_datasource sd on cs.id = sd.system_id
        where
        cs.id in (
        select
        distinct
        system_id
        from
        pre_source_config)
        <if test="searchValue != null and searchValue != ''">
            and (cs.name like concat('%', #{searchValue}, '%')
            or cs.introduce like concat('%', #{searchValue}, '%')
            )
        </if>
    </select>
</mapper>
