<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.CoAssetViewMapper">
    <delete id="deleteByOperationIdAndDbConfig">
        DELETE from co_asset_view
        <where>
            operation_id = #{operationId}
            AND
            <foreach collection="dbConfigList" item="dbConfig" separator="OR">
                (
                db_config::json ->> 'host' = #{dbConfig.host}
                AND db_config::json ->> 'port' = #{dbConfig.port}
                AND db_config::json ->> 'configType' = #{dbConfig.configType}
                <if test="dbConfig.dbName != null">
                    AND db_config::json ->> 'dbName' = #{dbConfig.dbName}
                </if>
                AND db_config::json ->> 'schemaName' = #{dbConfig.schemaName}
                )
            </foreach>
        </where>
    </delete>
    <select id="selectByQuery" resultType="com.dcas.common.domain.entity.CoAssetView">
        select * from co_asset_view where operation_id = #{query.operationId} and data_asset = #{query.dataAsset}
        <if test="query.dbType != null and query.dbType != ''">
            and db_type = #{query.dbType}
        </if>
        <if test="query.busSystem != null and query.busSystem != ''">
            and bus_system = #{query.busSystem}
        </if>
    </select>
</mapper>