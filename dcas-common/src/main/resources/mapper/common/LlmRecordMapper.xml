<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.LlmRecordMapper">
    <select id="recordQuery" resultType="com.dcas.common.model.vo.LlmRecordVO">
        select t1.id, t3.operation_name, t1.create_time, t2.label_id, t1.model_name, t1.status, t1.create_by
        from llm_record t1
                 inner join llm_task t2 on t1.task_id = t2.id
                 inner join co_operation t3 on t2.operation_id = t3.operation_id
        <where>
            <if test="query.operationName != null and query.operationName != ''">
                and t3.operation_name like concat('%', #{query.operationName}, '%')
            </if>
            <if test="query.labelId != null">
                and t2.label_id = #{query.labelId}
            </if>
            <if test="query.modelName != null and query.modelName != ''">
                and t1.model_name ilike concat('%', #{query.modelName}, '%')
            </if>
            <if test="query.status != null">
                and t1.status = #{query.status}
            </if>
            <if test="query.createBy != null and query.createBy != ''">
                and t1.create_by = #{query.createBy}
            </if>
            <if test="query.createTime != null">
                and t1.create_time &gt;= #{query.startTime}
            </if>
        </where>
    </select>
</mapper>