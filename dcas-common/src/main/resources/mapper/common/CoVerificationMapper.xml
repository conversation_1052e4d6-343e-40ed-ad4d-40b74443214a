<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.CoVerificationMapper">

    <select id="queryAll" resultType="CoVerification">
        select model_code as modelCode,
               item,
               demand,
               remarks,
               result
        from co_verification
    </select>

    <select id="queryVerificationList" resultType="CoVerification">
        select *
        from co_verification
        <where>
            <if test="operationId !=null and operationId !=''">
                and operation_id = #{operationId}
            </if>
            <if test="modelName !=null and modelName != ''">
                and model_name = #{modelName}
            </if>
        </where>
        order by sort asc
    </select>

    <select id="queryListByGroup" resultType="CoVerification">
        select verification_id as verificationId,
        bp_code as bpCode,
        remarks,
        result
        from co_verification
        <where>
            <if test="operationId !=null and operationId !=''">
                and operation_id = #{operationId}
            </if>
            <if test="modelName !=null and modelName != ''">
                and model_name = #{modelName}
            </if>
        </where>
    </select>
    <select id="queryVerificationLevel" resultType="java.lang.Integer">
        SELECT DISTINCT "level" FROM "co_verification" WHERE "operation_id" = #{operationId}
    </select>
    <select id="selectAdviceByOperationId" resultType="com.dcas.common.model.vo.AdviseMergeVO">
        SELECT distinct i.id             AS itemId,
                        ds.describe,
                        i.tag_ids        AS itemTag,
               cv.system_result AS systemResult
        FROM co_verification cv
                 INNER JOIN standard_item si ON cv.bp_code = si.bp_code
                 INNER JOIN clause_item_relevance cir ON si.id = cir.clause_id
                 INNER JOIN item i ON cir.item_id = i.id
                 INNER JOIN disposal_strategy ds ON i.id = ds.item_id
        WHERE cv.operation_id = #{operationId}
          AND cv.result = #{level}
        <if test="tagId != null and tagId != ''">
            and #{tagId} != any(string_to_array(i.tag_ids, ','))
        </if>
    </select>
    <select id="selectStandardIdByOperationId" resultType="java.lang.Integer">
        select distinct t3.standard_id
        from co_verification t1
                 inner join analysis_template t2 on t1.model_name = t2.name
                 inner join standard_template_relevance t3 on t3.template_id = t2.id
        where t1.operation_id = #{operationId}
    </select>
    <select id="selectTemplateFileByOperationId" resultType="java.lang.String">
        select distinct s.name as name
        from standard s
        where s.id in (select distinct t3.standard_id
                       from co_verification t1
                                inner join analysis_template t2 on t1.model_name = t2.name
                                inner join standard_template_relevance t3 on t3.template_id = t2.id
                       where t1.operation_id = #{operationId})
    </select>
    <select id="queryApplyModel" resultType="java.lang.String">
        SELECT DISTINCT "model_name" FROM "co_verification" WHERE "operation_id" = #{operationId}
    </select>
    <select id="queryVerificationStage" resultType="java.lang.String">
        SELECT DISTINCT "stage" FROM "co_verification" WHERE "operation_id" = #{operationId}
    </select>
    <select id="queryDimensionByOperationId" resultType="java.lang.String">
        SELECT DISTINCT "gp_dimension" FROM "co_verification" WHERE "operation_id" = #{operationId}
    </select>
    <select id="queryApplyTemplateId" resultType="java.lang.Long">
        SELECT DISTINCT "analysis_id" FROM "co_verification" WHERE "operation_id" = #{operationId}
    </select>
    <select id="queryMaxLevel" resultType="java.lang.String">
        SELECT trim(max(level)) FROM "co_verification" WHERE "operation_id" = #{operationId}
    </select>
    <select id="queryBpDetails" resultType="com.dcas.common.model.dto.QueryVerificationDetailDTO">
        select
        cv.*,
        scr.score as bpScore
        from
        "co_verification" cv
        left join spec_calc_result scr on
        cv.bp_code =
        scr.bp_code
        and cv.operation_id = scr.operation_id
        where
        cv."operation_id" = #{operationId}
        order by bp_code
    </select>
    <select id="queryCountByOperationId" resultType="com.dcas.common.model.dto.ResultCountDTO">
        select
        "result" ,count(1) as count
        from
        "co_verification" where operation_id =#{operationId} group by "result" ;
    </select>
    <select id="queryVerificationListByOperationId" resultType="com.dcas.common.domain.entity.CoVerification">
        select *
        from co_verification cv left join standard_item si ON cv.standard_id = si.standard_id and si.bp_code = cv.bp_code
        <where>
            operation_id = #{operationId}
            <if test="standardId != null">
                and cv.standard_id = #{standardId}
            </if>
        </where>
        order by cv.sort asc
    </select>
    <select id="getBpCodeRiskDesc" resultType="com.dcas.common.model.dto.QuestionnaireResultDTO">
        select
        cv.bp_code as bpCode,
        cir.item_id as item,
        rs."describe" as content
        from
        co_verification cv
        inner join standard_item si on
        cv.bp_code = si.bp_code
        inner join clause_item_relevance cir on si.id  = cir.clause_id
        left join risk_strategy rs on
        rs.item_id = cir.item_id
        where cv.operation_id = #{operationId}
    </select>
    <select id="queryBusSystemVerificationResultByOperationIds"
            resultType="com.dcas.common.model.dto.BusSystemResultDTO">
        select cl.operation_id,cl."result" ,cl.system_result ,co.create_time from co_verification cl left join co_operation co
        on cl.operation_id = co.operation_id
        where cl.operation_id  in
        <foreach collection="operationIds" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="countStandardFileSelected" resultType="com.dcas.common.model.dto.PieDataDTO">
        select cv.standard_id, s.name as name ,count(1) as value
        from co_verification cv inner join standard s on s.id = cv.standard_id where cv.operation_id  in
        <foreach collection="operationIds" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
        group by cv.standard_id, s.name
    </select>
    <select id="queryDetailById" resultType="com.dcas.common.model.dto.VerificationDetailDTO">
        select t1.id, t1.checked, inapplicable, t2.describe
        from questionnaire_content t1
                 left join risk_strategy t2 on t1.item_id = t2.item_id
        where t1.id in
        <foreach collection="list" item="id"  open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>


</mapper>
