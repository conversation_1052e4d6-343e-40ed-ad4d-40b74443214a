<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dcas.common.mapper.AdviseRiskMapper">
    <select id="selectItemIdToFilter" resultType="java.lang.String">
        select distinct item_id
        from advise_risk
        where operation_id = #{operationId} and type = #{type}
    </select>
    <select id="pageQuery" resultType="com.dcas.common.model.vo.AdviseRiskVO">
        select ar.id, rs.describe itemTitle, ar.risk_type, ar.content, ar.system_id, ar.risk_level
        from advise_risk ar
            left join risk_strategy rs on ar.item_id = rs.item_id
        <where>
            ar.operation_id = #{param.operationId} and ar.status = true and ar.type = #{param.type}
        <if test="param.itemTitle != null and param.itemTitle != ''">
            and rs.describe like concat('%', #{param.itemTitle}, '%')
        </if>
        <if test="param.riskType != null and param.riskType != ''">
            and ar.risk_type = #{param.riskType}
        </if>
        <if test="param.riskLevel != null and param.riskLevel != ''">
            and ar.risk_level = #{param.riskLevel}
        </if>
        <if test="param.content != null and param.content != ''">
            and ar.content like concat('%', #{param.content}, '%')
        </if>
        <if test="param.systemId != null and param.systemId > 0">
            and ar.system_id like concat('%', #{param.systemId}, '%')
        </if>
        </where>
    </select>
    <select id="selectRiskContent" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        select ar.id, ar.item_id,ar.system_id, ar.risk_type, ar.content, ar.risk_level, t.laws as basis, rs."describe", i.tag_ids, rs.risk_type as riskTypeTag
        from advise_risk ar
        left join (SELECT ari.item_id,
        STRING_AGG(concat(l.name, ' ', left(li.content, position('条' in li.content))), ', ') AS laws
        FROM article_item_relevance ari
        INNER JOIN law_item li ON ari.article_id = li.id
        INNER JOIN law l ON l.id = li.doc_id
        GROUP BY ari.item_id) t on ar.item_id = t.item_id
        left join risk_strategy rs on rs.item_id = ar.item_id
        left join item i on i.id = ar.item_id
        where ar.operation_id = #{operationId};
    </select>
    <select id="selectRiskContentWithLawIds" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        select ar.id, ar.item_id,ar.system_id, ar.risk_type, ar.content, ar.risk_level,rs."describe"
        from advise_risk ar
        left join risk_strategy rs on rs.item_id = ar.item_id
        where ar.type = 2 and ar.operation_id = #{operationId};
    </select>
    <select id="queryAdviseRiskByOperationIds" resultType="com.dcas.common.model.dto.BusSystemResultDTO">
        select ar.operation_id, ar.system_id as busSystem, ar.risk_level as level, co.create_time from advise_risk ar
        left join co_operation co on co.operation_id = ar.operation_id
        where ar.operation_id in
        <foreach collection="operationIds" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>
    <select id="selectRiskContentByType" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        select ar.id, ar.item_id,ar.system_id, ar.risk_type, ar.content, ar.risk_level, t.laws as basis, rs."describe"
        from advise_risk ar
        left join (SELECT ari.item_id,
        STRING_AGG(concat(l.name, ' ', left(li.content, position('条' in li.content))), ', ') AS laws
        FROM article_item_relevance ari
        INNER JOIN law_item li ON ari.article_id = li.id
        INNER JOIN law l ON l.id = li.doc_id
        GROUP BY ari.item_id) t on ar.item_id = t.item_id
        left join risk_strategy rs on rs.item_id = ar.item_id
        where ar.operation_id = #{operationId}
        and ar."type" = #{type};
    </select>
    <select id="selectLawRiskContentByType" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        select  ar.content, t.lawItemId as itemId
        from advise_risk ar
        left join (SELECT ari.item_id,li.id as lawItemId,
        STRING_AGG(concat(l.name, ' ', left(li.content, position('条' in li.content))), ', ') AS laws
        FROM article_item_relevance ari
        INNER JOIN law_item li ON ari.article_id = li.id
        INNER JOIN law l ON l.id = li.doc_id
        GROUP BY ari.item_id, li.id) t on ar.item_id = t.item_id
        left join risk_strategy rs on rs.item_id = ar.item_id
        where ar.operation_id = #{operationId}
        and ar."type" = #{type}
        group by ar.content,t.lawItemId;
    </select>
    <select id="selectLawBasicByItemIds" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        SELECT ari.item_id,
        STRING_AGG(concat(l.name, ' ', left(li.content, position('条' in li.content))), ', ') AS basis
        FROM article_item_relevance ari
        INNER JOIN law_item li ON ari.article_id = li.id
        INNER JOIN law l ON l.id = li.doc_id and l.id in (select distinct cl.law_id :: integer from co_legal cl where cl.operation_id = #{operationId})
        <if test="itemIds != null and itemIds.size()>0">
            where ari.item_id in
            <foreach collection="itemIds" open="(" separator="," close=")" item="itemId">
                #{itemId}
            </foreach>
        </if>
        GROUP BY ari.item_id
    </select>
    <select id="selectAnalysisBasicByItemIds" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        SELECT ari.item_id, STRING_AGG(concat(s.name, ' ', si.bp_code), ', ') AS basis
        FROM clause_item_relevance ari
        INNER JOIN standard_item si  ON ari.clause_id = si.id
        INNER JOIN standard s  ON s.id = si.standard_id  and s.id in (select distinct cv.standard_id from co_verification cv where cv.operation_id = #{operationId})
        <if test="itemIds != null and itemIds.size()>0">
            where ari.item_id in
            <foreach collection="itemIds" open="(" separator="," close=")" item="itemId">
                #{itemId}
            </foreach>
        </if>
        GROUP BY ari.item_id
    </select>
    <select id="selectRiskContentContainRiskType" resultType="com.dcas.common.model.dto.AdviseRiskDTO">
        select ar.id, ar.item_id,ar.system_id, rs.risk_type as riskTypeTag, ar.content, ar.risk_level, t.laws as basis, rs."describe", i.tag_ids
        from advise_risk ar
        left join (SELECT ari.item_id,
        STRING_AGG(concat(l.name, ' ', left(li.content, position('条' in li.content))), ', ') AS laws
        FROM article_item_relevance ari
        INNER JOIN law_item li ON ari.article_id = li.id
        INNER JOIN law l ON l.id = li.doc_id
        GROUP BY ari.item_id) t on ar.item_id = t.item_id
        left join risk_strategy rs on rs.item_id = ar.item_id
        left join item i on i.id = ar.item_id
        where rs.risk_type is not null and ar.operation_id = #{operationId};
    </select>
</mapper>