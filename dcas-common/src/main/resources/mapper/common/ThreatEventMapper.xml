<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcas.common.mapper.ThreatEventMapper">

    <select id="getThreatEventByThreatLibraryId" resultType="java.lang.String">
        select distinct event_time from threat_event te inner join threat_event_relation ter on te.id = ter.threat_event_id where ter.threat_library_id = #{threatLibraryId};
    </select>
</mapper>
