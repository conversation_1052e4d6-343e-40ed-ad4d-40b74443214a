package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.dcas.common.model.other.IntegerArrayTypeHandler;
import com.dcas.common.model.other.LongArrayTypeHandler;
import com.dcas.common.model.other.StringArrayTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

/**
 * <p>
 * 大屏配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "screen_config", autoResultMap = true)
public class ScreenConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 大屏名称
     */
    @TableField("name")
    private String name;

    /**
     * 业务系统id数组
     */
    @TableField(value = "bus_system_ids", jdbcType = JdbcType.ARRAY, typeHandler = IntegerArrayTypeHandler.class)
    private Integer[] busSystemIds;

    /**
     * 业务系统名称数组
     */
    @TableField(value = "bus_system_names", jdbcType = JdbcType.ARRAY, typeHandler = StringArrayTypeHandler.class)
    private String[] busSystemNames;

    /**
     * 数据更新方式：1-连接数源动态监测；2-根据已结项作业更新
     */
    @TableField("data_update_type")
    private Integer dataUpdateType;

    /**
     * 检测频率
     */
    @TableField("monitor_frequency")
    private Integer monitorFrequency;

    /**
     * 检测频率单位，默认：小时（h）
     */
    @TableField("monitor_frequency_unit")
    private String monitorFrequencyUnit;

    /**
     * 删除标志
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 大屏模板
     */
    @TableField("template")
    private String template;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
