package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/4/2 11:51
 * @since 1.7.2
 */
@Data
@TableName("inner_sys_rule_detail")
public class InnerSysRuleDetail {

    @TableId(type = IdType.AUTO)
    private Long sequence;

    private Long bizId;

    private Byte ruleType;

    private Byte algorithm;

    private Byte matchType;

    private Byte expect;

    private Long dictId;

    private String regex;

    private String function;

    private String enumContent;

    private Integer priority;

    private Integer degree;

    private Boolean status;

    private String relationTable;

    private String lengthLimit;

    private String substr;
}
