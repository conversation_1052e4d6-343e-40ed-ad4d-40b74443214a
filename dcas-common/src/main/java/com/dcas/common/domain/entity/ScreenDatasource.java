package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 大屏数源
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("screen_datasource")
public class ScreenDatasource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务系统ID
     */
    @TableField("system_id")
    private Integer systemId;

    /**
     * 数源ID
     */
    @TableField("source_id")
    private Integer sourceId;

    /**
     * 状态：0-失败 1-成功
     */
    @TableField("status")
    private Integer status;

    /**
     * 删除标志
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;


}
