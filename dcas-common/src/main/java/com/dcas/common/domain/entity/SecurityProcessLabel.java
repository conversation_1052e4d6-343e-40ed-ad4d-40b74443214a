package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/3 10:47
 * @since 1.4.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("security_process_label")
public class SecurityProcessLabel {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "安全作业id")
    private Integer securityId;

    @ApiModelProperty(value = "检查模块")
    private String model;

    @ApiModelProperty(value = "检查类别")
    private String category;

    @ApiModelProperty(value = "检查类别排序")
    private Integer sort;

    @ApiModelProperty(value = "检查内容")
    private String content;

    @ApiModelProperty(value = "是否需要上传文件")
    private Boolean needUpload;

    @ApiModelProperty(value = "文件id")
    private String fileIds;

    @ApiModelProperty(value = "是否已完成")
    private Boolean finished;
}
