package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/8 16:14
 * @since 1.0.0
 */
@Data
@TableName("co_system")
public class CoSystem {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String name;

    private String introduce;
}
