package com.dcas.common.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/7 10:30
 * @since 1.0.0
 */
@Data
@TableName("screen_hot_resources")
public class ScreenHotResources {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 热点资源类型 {@link com.dcas.common.enums.HotResourcesType}
     */
    @TableField("type")
    private Integer type;

    /**
     * 检查点id
     */
    @TableField("term_id")
    private Integer termId;

    /**
     * 业务系统id
     */
    @TableField("system_id")
    private Integer systemId;

    /**
     * 数据源id
     */
    @TableField("source_id")
    private Integer sourceId;


    /**
     * 热点资源名称
     */
    @TableField("name")
    private String name;

    /**
     * 热点资源访问次数
     */
    @TableField("access_count")
    private Long accessCount;
}
