package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/9 11:09
 * @since 1.0.0
 */
@Data
public class SystemSourceAdd {

    @ApiModelProperty(value = "业务系统ID")
    @NotNull(message = "业务系统ID不能为空")
    private Long id;

    @ApiModelProperty(value = "数据源ID")
    @NotNull(message = "数据源ID不能为空")
    private Integer configId;

    @ApiModelProperty(value = "schemas")
    private List<String> schemas;
}
