package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 大屏配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class ScreenDatasourceVO {

    /**
     * 业务系统ID
     */
    @ApiModelProperty("业务系统ID")
    private Integer systemId;

    /**
     * 业务系统名称
     */
    @ApiModelProperty("业务系统")
    private String busSystem;

    /**
     * 业务系统简介
     */
    @ApiModelProperty("业务系统简介")
    private String intro;

    /**
     * 业务系统id数组
     */
    @ApiModelProperty("关联数源列表")
    private List<SourceConfigVO> sourceConfigList;
}
