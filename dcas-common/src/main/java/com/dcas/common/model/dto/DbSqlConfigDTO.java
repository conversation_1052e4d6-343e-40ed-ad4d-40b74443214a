package com.dcas.common.model.dto;

import com.dcas.common.domain.entity.DbSqlConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/2/18 17:05
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DbSqlConfigDTO extends DbSqlConfig {
    private static final long serialVersionUID = 7567521087967059062L;

    private String termName;
}
