package com.dcas.common.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 大屏数源
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class ScreenDatasourceDTO{

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 业务系统ID
     */
    @ApiModelProperty("业务系统ID")
    private Integer systemId;

    /**
     * 业务系统名称
     */
    @ApiModelProperty("业务系统名称")
    private String busSystem;

    /**
     * 业务系统简介
     */
    @ApiModelProperty("业务系统简介")
    private String intro;

    /**
     * 数源ID
     */
    @ApiModelProperty("数源ID")
    private Integer sourceId;

    /**
     * 状态：0-失败 1-成功
     */
    @ApiModelProperty("状态：0-失败 1-成功")
    private Integer status;
}
