package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 基础环境危险分析-按数据库分析结果
 *
 * <AUTHOR>
 * @Date 2022/9/19 9:48
 * @ClassName QueryViewLoopholeByDBVo
 */
@ApiModel
@Data
@Builder
public class QueryViewLoopholeByDatabaseVo implements Serializable {
    /**
     * 数据库类型
     */
    @ApiModelProperty(value = "数据库类型")
    private String dbType;
    /**
     * ip地址
     */
    @ApiModelProperty(value = "ip地址")
    private String ip;
    /**
     * 极高危险比例
     */
    @ApiModelProperty(value = "极高危险比例")
    private BigDecimal highestFactor;
    /**
     * 高危险比例
     */
    @ApiModelProperty(value = "高危险比例")
    private BigDecimal highFactor;
    /**
     * 中危险比例
     */
    @ApiModelProperty(value = "中危险比例")
    private BigDecimal mediumFactor;
    /**
     * 低危险比例
     */
    @ApiModelProperty(value = "低危险比例")
    private BigDecimal lowFactor;

}
