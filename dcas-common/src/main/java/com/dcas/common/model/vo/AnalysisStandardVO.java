package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/13 16:16
 * @since 1.2.0
 */
@Data
public class AnalysisStandardVO {

    private String name;

    private String bpCode;

    private String stage;

    private String process;

    private String dimension;

    private String content;

    private String level;

    private String classify;

    private String assign;

    private String tags;

    private Boolean hasException;

    private String conditionTags;

    @ApiModelProperty(value = "是否启用调节因子，true-启用，false-不启用")
    private Boolean enableFactor;

    @ApiModelProperty(value = "调节因子")
    private String regulatoryFactor;

    @ApiModelProperty(value = "默认调节因子")
    private Double defaultRegulatoryFactor;

    @ApiModelProperty("文件ID")
    private Integer standardId;
    @ApiModelProperty("文件名")
    private String standardFile;
    @ApiModelProperty("文件简称")
    private String introduce;
    @ApiModelProperty("排序")
    private Integer sort;
}
