package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/10 17:03
 * @since 1.0.0
 */
@Data
public class SystemDetailVO {

    @ApiModelProperty(value = "系统ID")
    private Integer id;

    @ApiModelProperty(value = "系统名称")
    private String name;

    @ApiModelProperty(value = "系统简介")
    private String introduce;

    @ApiModelProperty(value = "系统关联数据源列表")
    private List<SystemSourceVO> sourceList;
}
