package com.dcas.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/12/8 11:46
 * @since 1.6.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DbConfig implements Serializable {
    private static final long serialVersionUID = 6073897795914181184L;

    private String host;

    private String port;

    private String configType;

    private String dbName;

    private String schemaName;
}
