package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/9 10:36
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemEditDTO extends SystemAddDTO {

    @ApiModelProperty(value = "业务系统ID")
    @NotNull(message = "业务系统ID不能为空")
    private Integer id;
}
