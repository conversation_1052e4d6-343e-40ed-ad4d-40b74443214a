package com.dcas.common.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/6/3 18:16
 * @since 1.6.6
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthorityUserExportExcel extends AuthorityExportExcel {

    @ExcelProperty(value = "用户名", index = 6)
    private String username;

    @ExcelProperty(value = "是否具有增删改查权限", index = 7)
    private String crudPri;

    @ExcelProperty(value = "是否具有插入权限", index = 8)
    private String insertPri;

    @ExcelProperty(value = "是否具有删除权限", index = 9)
    private String dropPri;

    @ExcelProperty(value = "是否具有删除（表）权限", index = 10)
    private String deletePri;

    @ExcelProperty(value = "是否具有更新权限", index = 11)
    private String updatePri;

    @ExcelProperty(value = "是否具有查询权限", index = 12)
    private String selectPri;
}
