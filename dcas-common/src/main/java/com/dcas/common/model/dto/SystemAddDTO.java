package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/9 10:18
 * @since 1.0.0
 */
@Data
public class SystemAddDTO {

    @ApiModelProperty(value = "业务系统名称")
    @NotBlank(message = "业务系统名称不能为空")
    @Size(max = 50, message = "业务系统名称不能超过50个字符")
    private String name;

    @ApiModelProperty(value = "业务系统简介")
    @NotBlank(message = "业务系统简介不能为空")
    @Size(max = 300, message = "业务系统简介不能超过300个字符")
    private String introduce;
}
