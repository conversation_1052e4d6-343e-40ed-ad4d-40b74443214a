package com.dcas.common.model.dto;

import com.dcas.common.model.query.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/9 10:40
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemQueryDTO extends PageQuery {
    private static final long serialVersionUID = -3394129880156731272L;

    @ApiModelProperty(value = "查询筛选作业绑定业务系统数据源必传")
    private String operationId;

    private String name;

    private String introduce;

    @ApiModelProperty(value = "类型：1.作业调用 2.大屏配置调用")
    private Integer type;
}
