package com.dcas.common.model.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/13 10:45
 * @since 1.2.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ComplianceLawItemVO extends ComplianceLawVO{

    /**
     * 法条关联核查项id
     */
    private String itemId;
    /**
     * 发条关联核查项名称
     */
    private String describe;

    /**
     * 核查项风险描述
     */
    private String riskDesc;
}
