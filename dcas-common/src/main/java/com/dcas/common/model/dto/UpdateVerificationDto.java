package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * fetch data
 *
 * <AUTHOR>
 * @Date 2022/9/9 14:21
 * @ClassName EditVerificationDto
 */
@ApiModel
@Data
public class UpdateVerificationDto implements Serializable {
    /**
     * 核验id
     */
    @ApiModelProperty(value = "现状核验id", required = true)
    @NotBlank(message = "现状核验id不能为空")
    private String verificationId;

    /**
     * 模板编码
     */
    @ApiModelProperty(value = "BP编码")
    private String bpCode;

    /**
     * 能力项/GP维度
     */
    @ApiModelProperty(value = "能力项")
    private String gpDimension;

    /**
     * 能力要求（标准条款要求）
     */
    @ApiModelProperty(value = "能力要求")
    private String standardProvision;

    /**
     * 现状描述（问卷备注）
     */
    @ApiModelProperty(value = "现状描述")
    private String description;

    @ApiModelProperty(value = "不符合现状描述")
    private String incompatible;

    /**
     * 符合性判断结果
     */
    @ApiModelProperty(value = "符合性判断结果")
    private String result;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注/理由")
    private String remarks;

    @ApiModelProperty(value = "调节因子")
    private Double regulatoryFactor;

    @ApiModelProperty(value = "作业ID")
    private String operationId;
}
