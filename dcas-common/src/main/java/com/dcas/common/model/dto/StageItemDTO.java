package com.dcas.common.model.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @className StageItemDTO
 * @description 阶段对应的核查项ID
 * @date 2024/07/24 09:17
 */
@Getter
@Setter
public class StageItemDTO {

    @ApiModelProperty(name = "阶段")
    private String stage;
    @ApiModelProperty(name = "核查项ID")
    private String itemId;

    public String getSplitStage(){
        List<String> stageList = CharSequenceUtil.split(stage, StrPool.C_SPACE);
        if (CollUtil.isNotEmpty(stageList) && stageList.size() > 1){
            return CharSequenceUtil.subAfter(stage, StrPool.C_SPACE, true);
        } else {
            return stage;
        }
    }
}
