package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 大屏配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class ScreenConfigDTO {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 大屏名称
     */
    @ApiModelProperty("大屏名称")
    private String name;

    /**
     * 业务系统id数组
     */
    @ApiModelProperty("业务系统id数组")
    @NotNull(message = "业务系统id数组不能为空")
    private Integer[] busSystemIds;

    /**
     * 业务系统名称数组
     */
    @ApiModelProperty("业务系统名称数组")
    @NotNull(message = "业务系统名称数组不能为空")
    private String[] busSystemNames;

    /**
     * 数据更新方式：1-连接数源动态监测；2-根据已结项作业更新
     */
    @ApiModelProperty("数据更新方式：1-连接数源动态监测；2-根据已结项作业更新")
    @NotNull(message = "数据更新方式不能为空")
    private Integer dataUpdateType;

    /**
     * 检测频率
     */
    @ApiModelProperty("检测频率")
    private Integer monitorFrequency;

    /**
     * 检测频率单位，默认：小时（h）
     */
    @ApiModelProperty("检测频率单位，默认：小时（h）")
    private String monitorFrequencyUnit;

    /**
     * 删除标志
     */
    @ApiModelProperty("删除标志")
    private Boolean delFlag;

    /**
     * 大屏模板
     */
    @ApiModelProperty("大屏模板")
    @NotEmpty(message = "大屏模板不能为空")
    private String template;
}
