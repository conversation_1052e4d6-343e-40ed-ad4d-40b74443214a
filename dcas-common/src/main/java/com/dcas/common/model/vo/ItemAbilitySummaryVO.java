package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className ItemAbilitySummaryVO
 * @description 核查项技术能力汇总
 * @date 2025/03/20 09:14
 */
@Data
public class ItemAbilitySummaryVO {

    @ApiModelProperty("数源名称")
    private String name;
    @ApiModelProperty("数据库检测符合情况汇总")
    private AbilitySummary dbSummary;
    @ApiModelProperty("技术检测符合情况汇总")
    private AbilitySummary tecSummary;

    @Getter
    @Setter
    public static class AbilitySummary {
        @ApiModelProperty("具备数量")
        private Integer countA;
        @ApiModelProperty("部分具备数量")
        private Integer countB;
        @ApiModelProperty("不具备数量")
        private Integer countC;
    }
}
