package com.dcas.common.model.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/06/14 14:28
 * @since 1.6.6
 */
@Data
public class AuthNodeVO {

    public AuthNodeVO() {
        this.authList = new ArrayList<>();
        this.child = new ArrayList<>();
    }

    private String id;

    private String name;

    private List<String> authList;

    private List<AuthNodeVO> child;
}
