package com.dcas.common.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.Map;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/7/4 10:07
 * @since 1.4.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OperationCopyDTO extends OperationAddDTO{

    @ApiModelProperty("被复制的作业ID")
    @NotBlank(message = "复制作业ID不能为空")
    private String operationId;

    @ApiModelProperty("新旧业务系统映射map，key:旧业务系统，value：新业务系统，没有修改可为空")
    private Map<String, String> busSystemMap;
}
