package com.dcas.common.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/1/19 16:23
 * @since 1.7.0
 */
@Data
public class DetermineContentVO {

    @ApiModelProperty(value = "资产id")
    private Integer id;

    @ApiModelProperty(value = "资产名称")
    private String name;

    @ApiModelProperty(value = "资产敏感等级")
    private Integer sensitiveLevel;

    @ApiModelProperty(value = "资产目录")
    private String belongs;

    @ApiModelProperty(value = "所属分类id")
    private Integer typeId;

    @ApiModelProperty(value = "所属分类层级")
    private Integer level;

    @ApiModelProperty(value = "资产描述")
    private String introduce;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    @ApiModelProperty(value = "数据标识 GENERAL-一般数据，CORE-核心数据，IMPORTANT-重要数据")
    private String dataTag;
}
