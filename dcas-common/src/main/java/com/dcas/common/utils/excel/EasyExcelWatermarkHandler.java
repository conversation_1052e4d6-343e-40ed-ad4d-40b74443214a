package com.dcas.common.utils.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * EasyExcel水印处理器
 * 用于在EasyExcel写入过程中添加水印
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class EasyExcelWatermarkHandler implements SheetWriteHandler {

    private final WatermarkConfig watermarkConfig;

    /**
     * 构造函数 - 使用默认配置
     *
     * @param watermarkText 水印文本
     */
    public EasyExcelWatermarkHandler(String watermarkText) {
        this.watermarkConfig = WatermarkConfig.defaultConfig(watermarkText);
    }

    /**
     * 在工作表创建后添加水印
     *
     * @param writeWorkbookHolder 工作簿持有者
     * @param writeSheetHolder 工作表持有者
     */
    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        try {
            Workbook workbook = writeWorkbookHolder.getWorkbook();
            ExcelWatermarkUtil.addWatermark(workbook, watermarkConfig);
            log.debug("EasyExcel水印添加成功，工作表: {}", writeSheetHolder.getSheetName());
        } catch (Exception e) {
            log.error("EasyExcel水印添加失败", e);
        }
    }

    /**
     * 创建简单水印处理器
     *
     * @param watermarkText 水印文本
     * @return 水印处理器
     */
    public static EasyExcelWatermarkHandler simple(String watermarkText) {
        return new EasyExcelWatermarkHandler(watermarkText);
    }

    /**
     * 创建自定义水印处理器
     *
     * @param watermarkText 水印文本
     * @param fontSize 字体大小
     * @param transparency 透明度
     * @return 水印处理器
     */
    public static EasyExcelWatermarkHandler custom(String watermarkText, int fontSize, float transparency) {
        WatermarkConfig config = WatermarkConfig.simpleConfig(watermarkText, fontSize, transparency);
        return new EasyExcelWatermarkHandler(config);
    }

    /**
     * 创建完全自定义水印处理器
     *
     * @param config 水印配置
     * @return 水印处理器
     */
    public static EasyExcelWatermarkHandler withConfig(WatermarkConfig config) {
        return new EasyExcelWatermarkHandler(config);
    }
}
