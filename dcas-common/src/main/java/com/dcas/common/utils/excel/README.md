# Excel水印功能使用指南

## 概述

本模块为DCAS项目提供了完整的Excel导出水印功能，支持与现有的EasyExcel和Hutool ExcelUtil无缝集成。

## 核心组件

### 1. WatermarkConfig - 水印配置类
配置水印的各种属性，包括文本、字体、颜色、透明度、旋转角度等。

```java
// 使用默认配置
WatermarkConfig config = WatermarkConfig.defaultConfig("机密文档");

// 自定义配置
WatermarkConfig config = WatermarkConfig.builder()
    .text("内部使用")
    .fontSize(24)
    .transparency(0.2f)
    .fontColor(new Color(255, 0, 0, 80))
    .rotationAngle(-30.0)
    .bold(true)
    .build();
```

### 2. ExcelWatermarkUtil - 核心水印工具类
提供添加水印的核心功能，支持POI Workbook对象。

```java
// 添加简单水印
ExcelWatermarkUtil.addSimpleWatermark(workbook, "机密文档");

// 添加自定义水印
ExcelWatermarkUtil.addWatermark(workbook, config);
```

### 3. EasyExcelWatermarkHandler - EasyExcel集成处理器
用于在EasyExcel导出过程中自动添加水印。

```java
// 简单使用
EasyExcel.write(outputStream, DataClass.class)
    .registerWriteHandler(EasyExcelWatermarkHandler.simple("机密文档"))
    .sheet("数据")
    .doWrite(dataList);
```

## 集成方式

### 方式1: 使用扩展的Func.export方法（推荐）

这是最简单的集成方式，只需要在现有的`Func.export`调用中添加水印参数：

```java
// 原有代码
Func.export(response, dataList, "导出文件名");

// 修改后的代码（添加水印）
Func.export(response, dataList, "导出文件名", "机密文档");

// 不添加水印（保持兼容性）
Func.export(response, dataList, "导出文件名", null);
```

### 方式2: 在Hutool ExcelWriter中手动添加

适用于需要更多控制的场景：

```java
try (ExcelWriter writer = ExcelUtil.getWriter()) {
    writer.setOnlyAlias(true);
    writer.write(data, true);
    
    // 添加水印
    ExcelWatermarkUtil.addSimpleWatermark(writer.getWorkbook(), "机密文档");
    
    Func.responseSetting(response, fileName + ".xlsx");
    writer.flush(response.getOutputStream(), true);
}
```

### 方式3: 在EasyExcel中使用水印处理器

适用于EasyExcel导出场景：

```java
// 普通导出
EasyExcel.write(outputStream, DataClass.class)
    .registerWriteHandler(EasyExcelWatermarkHandler.simple("机密文档"))
    .sheet("数据")
    .doWrite(dataList);

// 模板导出
EasyExcel.write(outputStream)
    .withTemplate(templatePath)
    .registerWriteHandler(EasyExcelWatermarkHandler.simple("机密文档"))
    .sheet()
    .doWrite(dataList);
```

## 实际应用示例

### 修改现有服务方法

以QuestionServiceImpl为例，展示如何最小化修改现有代码：

```java
// 原始方法
@Override
@SneakyThrows
public void export(HttpServletResponse response) {
    List<Question> list = this.pageList(1, Integer.MAX_VALUE).getList();
    List<BaseExcel> excels = list.stream().map(q -> BaseExcel.builder()
            .id(q.getId())
            .title(q.getTitle())
            .describe(q.getDescribe())
            .version("V" + q.getVersion())
            .objectTags(q.getObjectIds())
            .tags(q.getTagIds())
            .build()).collect(Collectors.toList());

    try (ExcelWriter writer = ExcelUtil.getWriter()) {
        writer.setOnlyAlias(true);
        writer.write(excels, true);
        
        // 添加这一行即可集成水印功能
        ExcelWatermarkUtil.addSimpleWatermark(writer.getWorkbook(), "内部使用");
        
        Func.responseSetting(response,
                String.format("调研问题导出-%s.xlsx", DateUtils.getExportDateStr(System.currentTimeMillis())));

        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
    }
}
```

### 根据业务场景选择水印

```java
public void exportWithBusinessWatermark(HttpServletResponse response, 
                                       List<Object> data, 
                                       String fileName, 
                                       int sensitivityLevel) throws IOException {
    String watermarkText;
    
    switch (sensitivityLevel) {
        case 1: // 公开
            watermarkText = null; // 不添加水印
            break;
        case 2: // 内部
            watermarkText = "内部使用";
            break;
        case 3: // 机密
            watermarkText = "机密文档";
            break;
        case 4: // 绝密
            // 使用自定义配置
            WatermarkConfig config = WatermarkConfig.builder()
                    .text("绝密文档")
                    .fontSize(28)
                    .transparency(0.15f)
                    .fontColor(new Color(255, 0, 0, 120))
                    .bold(true)
                    .build();
            
            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(data, true);
                ExcelWatermarkUtil.addWatermark(writer.getWorkbook(), config);
                Func.responseSetting(response, fileName + ".xlsx");
                writer.flush(response.getOutputStream(), true);
            }
            return;
        default:
            watermarkText = "内部使用";
    }
    
    Func.export(response, data, fileName, watermarkText);
}
```

## 配置选项

### 默认配置
- 字体：宋体
- 字体大小：20
- 颜色：浅灰色 (200, 200, 200, 100)
- 旋转角度：-45度
- 透明度：0.3
- 水平间距：200像素
- 垂直间距：150像素

### 常用配置示例

```java
// 机密文档水印
WatermarkConfig confidential = WatermarkConfig.builder()
        .text("机密文档")
        .fontSize(24)
        .transparency(0.2f)
        .fontColor(new Color(255, 0, 0, 80))
        .bold(true)
        .build();

// 草稿版本水印
WatermarkConfig draft = WatermarkConfig.builder()
        .text("草稿版本")
        .fontSize(18)
        .transparency(0.4f)
        .italic(true)
        .rotationAngle(-60.0)
        .build();
```

## 注意事项

1. **性能影响**：添加水印会增加少量的处理时间和文件大小，但影响很小
2. **兼容性**：完全兼容现有的导出功能，不会破坏现有代码
3. **错误处理**：水印添加失败不会影响正常的数据导出
4. **内存使用**：水印图片会临时占用内存，但会自动释放

## 测试建议

建议编写测试来验证水印功能：

```java
@Test
void testWatermarkIntegration() throws IOException {
    // 准备测试数据
    List<TestData> dataList = Arrays.asList(
            new TestData("张三", 25, "技术部"),
            new TestData("李四", 30, "销售部")
    );

    // 测试导出带水印的文件
    Path outputFile = tempDir.resolve("test_watermark.xlsx");
    try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
        EasyExcel.write(fos, TestData.class)
                .registerWriteHandler(EasyExcelWatermarkHandler.simple("测试水印"))
                .sheet("测试数据")
                .doWrite(dataList);
    }

    // 验证文件生成成功
    assertTrue(outputFile.toFile().exists());
    assertTrue(outputFile.toFile().length() > 0);
}
```

## 总结

本水印功能提供了：
- **易于集成**：最小化代码修改
- **灵活配置**：支持各种水印样式
- **多种方式**：支持Hutool和EasyExcel
- **向后兼容**：不影响现有功能
- **业务适配**：可根据不同场景选择不同水印
