package com.dcas.common.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * EasyExcel水印修复效果演示
 * 用于验证内容检测问题的修复效果
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
public class EasyExcelWatermarkFixDemo {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationData {
        @ExcelProperty("检查项目")
        private String checkItem;
        
        @ExcelProperty("检查结果")
        private String result;
        
        @ExcelProperty("风险等级")
        private String riskLevel;
        
        @ExcelProperty("建议措施")
        private String suggestion;
    }

    /**
     * 演示修复前后的对比
     * 模拟 CoVerificationServiceImpl#downloadExcel 的使用场景
     */
    public static void demonstrateFix() {
        log.info("开始演示EasyExcel水印修复效果...");
        
        // 准备测试数据（模拟能力评估数据）
        List<VerificationData> verificationData = Arrays.asList(
                new VerificationData("数据库访问控制", "通过", "低", "继续保持现有安全策略"),
                new VerificationData("网络安全防护", "部分通过", "中", "建议加强防火墙配置"),
                new VerificationData("数据加密传输", "未通过", "高", "立即实施SSL/TLS加密"),
                new VerificationData("用户权限管理", "通过", "低", "定期审查用户权限"),
                new VerificationData("日志审计机制", "部分通过", "中", "完善日志记录策略")
        );

        // 测试1: 使用修复后的原始处理器
        testOriginalHandlerFixed(verificationData);
        
        // 测试2: 使用V2处理器的不同模式
        testV2HandlerModes(verificationData);
        
        // 测试3: 模板导出场景
        testTemplateExportScenario(verificationData);
        
        log.info("EasyExcel水印修复效果演示完成！");
    }

    /**
     * 测试修复后的原始处理器
     */
    private static void testOriginalHandlerFixed(List<VerificationData> data) {
        log.info("=== 测试修复后的原始处理器 ===");
        
        try {
            String outputPath = "demo_output/fixed_original_handler.xlsx";
            
            EasyExcel.write(outputPath, VerificationData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple("机密文档"))
                    .sheet("能力评估结果")
                    .doWrite(data);
            
            log.info("✅ 原始处理器测试成功: {}", outputPath);
            log.info("   预期日志: 应该看到'成功为工作表添加水印'而不是'跳过水印添加'");
            
        } catch (Exception e) {
            log.error("❌ 原始处理器测试失败", e);
        }
    }

    /**
     * 测试V2处理器的不同模式
     */
    private static void testV2HandlerModes(List<VerificationData> data) {
        log.info("=== 测试V2处理器的不同模式 ===");
        
        // 模式1: 工作簿完成后添加（推荐）
        try {
            String outputPath = "demo_output/v2_workbook_dispose.xlsx";
            
            EasyExcel.write(outputPath, VerificationData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandlerV2.create("工作簿模式"))
                    .sheet("能力评估结果")
                    .doWrite(data);
            
            log.info("✅ V2工作簿模式测试成功: {}", outputPath);
            
        } catch (Exception e) {
            log.error("❌ V2工作簿模式测试失败", e);
        }

        // 模式2: 工作表完成后添加
        try {
            String outputPath = "demo_output/v2_sheet_dispose.xlsx";
            
            EasyExcel.write(outputPath, VerificationData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandlerV2.createForSheet("工作表模式"))
                    .sheet("能力评估结果")
                    .doWrite(data);
            
            log.info("✅ V2工作表模式测试成功: {}", outputPath);
            
        } catch (Exception e) {
            log.error("❌ V2工作表模式测试失败", e);
        }

        // 模式3: 回退模式
        try {
            String outputPath = "demo_output/v2_fallback_mode.xlsx";
            
            EasyExcel.write(outputPath, VerificationData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandlerV2.createFallback("回退模式"))
                    .sheet("能力评估结果")
                    .doWrite(data);
            
            log.info("✅ V2回退模式测试成功: {}", outputPath);
            
        } catch (Exception e) {
            log.error("❌ V2回退模式测试失败", e);
        }
    }

    /**
     * 测试模板导出场景
     */
    private static void testTemplateExportScenario(List<VerificationData> data) {
        log.info("=== 测试模板导出场景 ===");
        
        try {
            String outputPath = "demo_output/template_export_with_watermark.xlsx";
            
            // 注意：这里假设有模板文件，实际使用时需要提供真实的模板路径
            // 如果没有模板文件，这个测试会失败，但不影响其他测试
            
            log.info("模板导出测试（需要模板文件）:");
            log.info("EasyExcel.write(outputPath)");
            log.info("    .withTemplate(\"template/verification_template.xlsx\")");
            log.info("    .registerWriteHandler(EasyExcelWatermarkHandlerV2.createFallback(\"模板水印\"))");
            log.info("    .sheet()");
            log.info("    .doWrite(data);");
            
            log.info("💡 模板导出建议使用回退模式，确保在模板场景下也能正确添加水印");
            
        } catch (Exception e) {
            log.error("❌ 模板导出测试失败（可能是模板文件不存在）", e);
        }
    }

    /**
     * 验证修复效果的关键指标
     */
    public static void verifyFixEffectiveness() {
        log.info("=== 修复效果验证指标 ===");
        log.info("1. 日志检查:");
        log.info("   ✅ 应该看到: '检测到工作表 XXX 的内容边界: 行X-X, 列X-X'");
        log.info("   ✅ 应该看到: '成功为工作表 XXX 添加水印，覆盖区域: 行X-X, 列X-X'");
        log.info("   ❌ 不应该看到: '工作表 XXX 没有内容，跳过水印添加'");
        log.info("");
        log.info("2. 文件检查:");
        log.info("   ✅ 生成的Excel文件应该包含水印");
        log.info("   ✅ 水印应该覆盖实际数据区域");
        log.info("   ✅ 数据应该正常显示，不受水印影响");
        log.info("");
        log.info("3. 性能检查:");
        log.info("   ✅ 导出时间应该没有明显增加");
        log.info("   ✅ 内存使用应该保持正常");
        log.info("   ✅ 文件大小应该适中（水印图片大小合理）");
    }

    /**
     * 提供给CoVerificationServiceImpl使用的示例代码
     */
    public static void showCoVerificationServiceExample() {
        log.info("=== CoVerificationServiceImpl 使用示例 ===");
        log.info("");
        log.info("// 原有代码（现在已经修复，无需修改）:");
        log.info("EasyExcel.write(response.getOutputStream())");
        log.info("    .registerWriteHandler(EasyExcelWatermarkHandler.simple(\"机密文档\"))");
        log.info("    .sheet(\"能力评估\")");
        log.info("    .doWrite(dataList);");
        log.info("");
        log.info("// 如果需要更多控制，可以使用V2版本:");
        log.info("EasyExcel.write(response.getOutputStream())");
        log.info("    .registerWriteHandler(EasyExcelWatermarkHandlerV2.create(\"机密文档\"))");
        log.info("    .sheet(\"能力评估\")");
        log.info("    .doWrite(dataList);");
        log.info("");
        log.info("// 自定义水印样式:");
        log.info("WatermarkConfig config = WatermarkConfig.builder()");
        log.info("    .text(\"机密文档\")");
        log.info("    .fontSize(24)");
        log.info("    .transparency(0.2f)");
        log.info("    .build();");
        log.info("EasyExcel.write(response.getOutputStream())");
        log.info("    .registerWriteHandler(EasyExcelWatermarkHandlerV2.createWithConfig(config,");
        log.info("        EasyExcelWatermarkHandlerV2.WatermarkTiming.AFTER_WORKBOOK_DISPOSE))");
        log.info("    .sheet(\"能力评估\")");
        log.info("    .doWrite(dataList);");
    }

    /**
     * 主方法：运行所有演示
     */
    public static void main(String[] args) {
        log.info("🚀 EasyExcel水印修复演示开始");
        log.info("解决问题: 工作表 Sheet1 没有内容，跳过水印添加");
        log.info("");
        
        // 运行演示
        demonstrateFix();
        
        log.info("");
        
        // 验证指标
        verifyFixEffectiveness();
        
        log.info("");
        
        // 使用示例
        showCoVerificationServiceExample();
        
        log.info("");
        log.info("🎉 EasyExcel水印修复演示完成！");
        log.info("现在您可以在CoVerificationServiceImpl中正常使用水印功能了。");
    }
}
