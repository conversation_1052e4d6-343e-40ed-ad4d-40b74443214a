package com.dcas.common.utils.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.dcas.common.utils.Func;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

/**
 * Excel水印使用示例
 * 展示如何在现有的Excel导出功能中集成水印功能
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
public class ExcelWatermarkExample {

    /**
     * 示例1: 使用Func.export方法导出带水印的Excel
     * 这是最简单的集成方式，只需要在现有的Func.export调用中添加水印参数
     */
    public static <T> void exportWithWatermarkUsingFunc(HttpServletResponse response, 
                                                        Collection<T> data, 
                                                        String fileName, 
                                                        String watermarkText) throws IOException {
        // 使用扩展的Func.export方法，直接传入水印文本
        Func.export(response, data, fileName, watermarkText);
    }

    /**
     * 示例2: 使用Hutool ExcelWriter手动添加水印
     * 适用于需要更多控制的场景
     */
    public static <T> void exportWithCustomWatermark(HttpServletResponse response, 
                                                    Collection<T> data, 
                                                    String fileName, 
                                                    WatermarkConfig watermarkConfig) throws IOException {
        try (ExcelWriter writer = ExcelUtil.getWriter()) {
            writer.setOnlyAlias(true);
            writer.write(data, true);
            
            // 添加自定义水印
            ExcelWatermarkUtil.addWatermark(writer.getWorkbook(), watermarkConfig);
            
            Func.responseSetting(response, fileName + ".xlsx");
            writer.flush(response.getOutputStream(), true);
        }
    }

    /**
     * 示例3: 使用EasyExcel导出带水印的Excel
     * 适用于使用EasyExcel模板或复杂导出的场景
     */
    public static <T> void exportWithEasyExcelWatermark(OutputStream outputStream, 
                                                       List<T> data, 
                                                       Class<T> clazz, 
                                                       String watermarkText) {
        EasyExcel.write(outputStream, clazz)
                .registerWriteHandler(EasyExcelWatermarkHandler.simple(watermarkText))
                .sheet("数据导出")
                .doWrite(data);
    }

    /**
     * 示例4: 使用EasyExcel模板导出带水印的Excel
     * 适用于基于模板的导出场景
     */
    public static <T> void exportWithEasyExcelTemplateWatermark(OutputStream outputStream, 
                                                               List<T> data, 
                                                               String templatePath, 
                                                               String watermarkText) {
        EasyExcel.write(outputStream)
                .withTemplate(templatePath)
                .registerWriteHandler(EasyExcelWatermarkHandler.simple(watermarkText))
                .sheet()
                .doWrite(data);
    }

    /**
     * 示例5: 自定义水印样式的EasyExcel导出
     */
    public static <T> void exportWithCustomEasyExcelWatermark(OutputStream outputStream, 
                                                             List<T> data, 
                                                             Class<T> clazz, 
                                                             WatermarkConfig watermarkConfig) {
        EasyExcel.write(outputStream, clazz)
                .registerWriteHandler(EasyExcelWatermarkHandler.withConfig(watermarkConfig))
                .sheet("数据导出")
                .doWrite(data);
    }

    /**
     * 示例6: 修改现有导出方法以支持水印
     * 展示如何最小化修改现有代码来支持水印功能
     */
    public static void modifyExistingExportMethod() {
        /*
         * 原有代码:
         * Func.export(response, dataList, "导出文件名");
         * 
         * 修改后的代码:
         * Func.export(response, dataList, "导出文件名", "机密文档");
         * 
         * 或者保持原有调用不变（不添加水印）:
         * Func.export(response, dataList, "导出文件名");
         */
    }

    /**
     * 示例7: 在现有EasyExcel导出中添加水印
     */
    public static void modifyExistingEasyExcelExport() {
        /*
         * 原有代码:
         * EasyExcel.write(outputStream, DataClass.class)
         *     .sheet("数据")
         *     .doWrite(dataList);
         * 
         * 修改后的代码:
         * EasyExcel.write(outputStream, DataClass.class)
         *     .registerWriteHandler(EasyExcelWatermarkHandler.simple("机密文档"))
         *     .sheet("数据")
         *     .doWrite(dataList);
         */
    }

    /**
     * 创建不同类型的水印配置示例
     */
    public static class WatermarkConfigExamples {
        
        /**
         * 机密文档水印
         */
        public static WatermarkConfig confidentialWatermark() {
            return WatermarkConfig.builder()
                    .text("机密文档")
                    .fontSize(24)
                    .transparency(0.2f)
                    .fontColor(new java.awt.Color(255, 0, 0, 80))
                    .rotationAngle(-30.0)
                    .build();
        }

        /**
         * 内部使用水印
         */
        public static WatermarkConfig internalUseWatermark() {
            return WatermarkConfig.builder()
                    .text("内部使用")
                    .fontSize(18)
                    .transparency(0.3f)
                    .fontColor(new java.awt.Color(128, 128, 128, 100))
                    .rotationAngle(-45.0)
                    .horizontalSpacing(180)
                    .verticalSpacing(120)
                    .build();
        }

        /**
         * 草稿版本水印
         */
        public static WatermarkConfig draftWatermark() {
            return WatermarkConfig.builder()
                    .text("草稿版本")
                    .fontSize(20)
                    .transparency(0.4f)
                    .fontColor(new java.awt.Color(0, 0, 255, 60))
                    .rotationAngle(-60.0)
                    .italic(true)
                    .build();
        }
    }
}
