package com.dcas.common.utils.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.dcas.common.utils.DateUtils;
import com.dcas.common.utils.Func;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * Excel水印集成示例
 * 展示如何在现有的服务方法中集成水印功能
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
public class ExcelWatermarkIntegrationExample {

    /**
     * 示例1: 修改现有的QuestionService导出方法以支持水印
     * 原方法位于: dcas-system/src/main/java/com/dcas/system/service/impl/QuestionServiceImpl.java
     */
    public static class QuestionServiceWatermarkExample {
        
        /**
         * 原始导出方法（不带水印）
         */
        public void exportOriginal(HttpServletResponse response, List<Object> excels) throws IOException {
            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(excels, true);
                Func.responseSetting(response,
                        String.format("调研问题导出-%s.xlsx", DateUtils.getExportDateStr(System.currentTimeMillis())));

                ServletOutputStream out = response.getOutputStream();
                writer.flush(out, true);
            }
        }

        /**
         * 修改后的导出方法（带水印）
         * 只需要添加一行代码即可集成水印功能
         */
        public void exportWithWatermark(HttpServletResponse response, List<Object> excels) throws IOException {
            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(excels, true);
                
                // 添加水印 - 只需要这一行代码
                ExcelWatermarkUtil.addSimpleWatermark(writer.getWorkbook(), "内部使用");
                
                Func.responseSetting(response,
                        String.format("调研问题导出-%s.xlsx", DateUtils.getExportDateStr(System.currentTimeMillis())));

                ServletOutputStream out = response.getOutputStream();
                writer.flush(out, true);
            }
        }

        /**
         * 使用Func.export方法的简化版本
         */
        public void exportUsingFuncWithWatermark(HttpServletResponse response, List<Object> excels) throws IOException {
            // 使用扩展的Func.export方法，直接传入水印文本
            Func.export(response, excels, "调研问题导出", "内部使用");
        }
    }

    /**
     * 示例2: 修改现有的EasyExcel导出方法以支持水印
     * 参考: dcas-system/src/main/java/com/dcas/system/service/impl/DetectionResultServiceImpl.java
     */
    public static class DetectionResultServiceWatermarkExample {
        
        /**
         * 原始EasyExcel导出方法（不带水印）
         */
        public void exportOriginal(HttpServletResponse response, List<Object> detectionResults) throws IOException {
            Func.responseSetting(response, "基础评估风险检测结果.xlsx");
            EasyExcel.write(response.getOutputStream())
                    .withTemplate("template/detectionResult.xlsx")
                    .sheet()
                    .doWrite(detectionResults);
        }

        /**
         * 修改后的EasyExcel导出方法（带水印）
         * 只需要添加registerWriteHandler即可
         */
        public void exportWithWatermark(HttpServletResponse response, List<Object> detectionResults) throws IOException {
            Func.responseSetting(response, "基础评估风险检测结果.xlsx");
            EasyExcel.write(response.getOutputStream())
                    .withTemplate("template/detectionResult.xlsx")
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple("机密文档")) // 添加水印处理器
                    .sheet()
                    .doWrite(detectionResults);
        }
    }

    /**
     * 示例3: 在报告生成中集成水印
     * 参考: dcas-system/src/main/java/com/dcas/system/report/attachment/FinanceAttachmentReport.java
     */
    public static class ReportWatermarkExample {
        
        /**
         * 原始报告导出方法（不带水印）
         */
        public String exportReportOriginal(String outputPath, List<Object> result) throws IOException {
            EasyExcel.write(outputPath)
                    .withTemplate("template/inventoryAttachmentJrTemplate.xlsx")
                    .sheet()
                    .doWrite(result);
            return outputPath;
        }

        /**
         * 修改后的报告导出方法（带水印）
         */
        public String exportReportWithWatermark(String outputPath, List<Object> result) throws IOException {
            EasyExcel.write(outputPath)
                    .withTemplate("template/inventoryAttachmentJrTemplate.xlsx")
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple("数据安全评估报告")) // 添加水印
                    .sheet()
                    .doWrite(result);
            return outputPath;
        }
    }

    /**
     * 示例4: 根据不同业务场景使用不同的水印
     */
    public static class BusinessScenarioWatermarkExample {
        
        /**
         * 根据数据敏感级别选择水印
         */
        public void exportWithSensitivityWatermark(HttpServletResponse response, 
                                                  List<Object> data, 
                                                  String fileName, 
                                                  int sensitivityLevel) throws IOException {
            String watermarkText;
            WatermarkConfig config;
            
            switch (sensitivityLevel) {
                case 1: // 公开
                    watermarkText = null; // 不添加水印
                    break;
                case 2: // 内部
                    watermarkText = "内部使用";
                    break;
                case 3: // 机密
                    watermarkText = "机密文档";
                    break;
                case 4: // 绝密
                    config = WatermarkConfig.builder()
                            .text("绝密文档")
                            .fontSize(28)
                            .transparency(0.15f)
                            .fontColor(new java.awt.Color(255, 0, 0, 120))
                            .bold(true)
                            .build();
                    
                    try (ExcelWriter writer = ExcelUtil.getWriter()) {
                        writer.setOnlyAlias(true);
                        writer.write(data, true);
                        ExcelWatermarkUtil.addWatermark(writer.getWorkbook(), config);
                        Func.responseSetting(response, fileName + ".xlsx");
                        writer.flush(response.getOutputStream(), true);
                    }
                    return;
                default:
                    watermarkText = "内部使用";
            }
            
            // 使用简单水印或不添加水印
            Func.export(response, data, fileName, watermarkText);
        }

        /**
         * 根据用户角色选择水印
         */
        public void exportWithRoleBasedWatermark(HttpServletResponse response, 
                                                List<Object> data, 
                                                String fileName, 
                                                String userRole) throws IOException {
            String watermarkText;
            
            switch (userRole.toLowerCase()) {
                case "admin":
                case "manager":
                    watermarkText = "管理员专用";
                    break;
                case "auditor":
                    watermarkText = "审计专用";
                    break;
                case "guest":
                case "readonly":
                    watermarkText = "仅供查看";
                    break;
                default:
                    watermarkText = "内部使用";
            }
            
            Func.export(response, data, fileName, watermarkText);
        }
    }

    /**
     * 示例5: 批量文件处理中的水印应用
     */
    public static class BatchProcessingWatermarkExample {
        
        /**
         * 批量导出多个文件，每个文件都添加水印
         */
        public void batchExportWithWatermark(String outputDir, 
                                           List<ExportTask> tasks, 
                                           String commonWatermark) {
            tasks.parallelStream().forEach(task -> {
                try {
                    String filePath = outputDir + "/" + task.getFileName() + ".xlsx";
                    EasyExcel.write(filePath, task.getDataClass())
                            .registerWriteHandler(EasyExcelWatermarkHandler.simple(commonWatermark))
                            .sheet(task.getSheetName())
                            .doWrite(task.getData());
                    log.info("成功导出带水印文件: {}", filePath);
                } catch (Exception e) {
                    log.error("导出文件失败: {}", task.getFileName(), e);
                }
            });
        }
    }

    /**
     * 导出任务类
     */
    public static class ExportTask {
        private String fileName;
        private String sheetName;
        private Class<?> dataClass;
        private List<Object> data;
        
        // getters and setters
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }
        public Class<?> getDataClass() { return dataClass; }
        public void setDataClass(Class<?> dataClass) { this.dataClass = dataClass; }
        public List<Object> getData() { return data; }
        public void setData(List<Object> data) { this.data = data; }
    }
}
