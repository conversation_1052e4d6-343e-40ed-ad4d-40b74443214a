package com.dcas.common.utils.excel;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.WorkbookWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * EasyExcel水印处理器 V2 - 多时机支持版本
 * 支持在不同时机添加水印，解决各种场景下的内容检测问题
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class EasyExcelWatermarkHandlerV2 implements WorkbookWriteHandler, SheetWriteHandler {

    private final WatermarkConfig watermarkConfig;
    private final WatermarkTiming timing;
    private boolean watermarkAdded = false;

    /**
     * 水印添加时机枚举
     */
    public enum WatermarkTiming {
        /**
         * 在工作簿写入完成后添加（推荐）
         * 适用于大多数场景，确保数据已写入
         */
        AFTER_WORKBOOK_DISPOSE,
        
        /**
         * 在每个工作表写入完成后添加
         * 适用于多工作表场景
         */
        AFTER_SHEET_DISPOSE,
        
        /**
         * 在工作表创建后立即添加（兼容模式）
         * 使用预估的内容区域，适用于模板导出
         */
        AFTER_SHEET_CREATE_FALLBACK
    }

    /**
     * 构造函数 - 使用默认配置和推荐时机
     *
     * @param watermarkText 水印文本
     */
    public EasyExcelWatermarkHandlerV2(String watermarkText) {
        this.watermarkConfig = WatermarkConfig.defaultConfig(watermarkText);
        this.timing = WatermarkTiming.AFTER_WORKBOOK_DISPOSE;
    }

    /**
     * 构造函数 - 指定时机
     *
     * @param watermarkText 水印文本
     * @param timing 添加时机
     */
    public EasyExcelWatermarkHandlerV2(String watermarkText, WatermarkTiming timing) {
        this.watermarkConfig = WatermarkConfig.defaultConfig(watermarkText);
        this.timing = timing;
    }

    // WorkbookWriteHandler 接口实现

    @Override
    public void afterWorkbookCreate(WriteWorkbookHolder writeWorkbookHolder) {
        // 工作簿创建后不做任何操作
    }

    @Override
    public void afterWorkbookDispose(WriteWorkbookHolder writeWorkbookHolder) {
        if (timing == WatermarkTiming.AFTER_WORKBOOK_DISPOSE && !watermarkAdded) {
            addWatermarkToWorkbook(writeWorkbookHolder.getWorkbook(), "工作簿写入完成后");
        }
    }

    // SheetWriteHandler 接口实现

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (timing == WatermarkTiming.AFTER_SHEET_CREATE_FALLBACK) {
            addWatermarkWithFallback(writeWorkbookHolder.getWorkbook(), writeSheetHolder.getSheet());
        }
    }

    @Override
    public void afterSheetDispose(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        if (timing == WatermarkTiming.AFTER_SHEET_DISPOSE) {
            addWatermarkToSheet(writeWorkbookHolder.getWorkbook(), writeSheetHolder.getSheet(), "工作表写入完成后");
        }
    }

    /**
     * 为整个工作簿添加水印
     */
    private void addWatermarkToWorkbook(Workbook workbook, String stage) {
        try {
            ExcelWatermarkUtil.addWatermark(workbook, watermarkConfig);
            watermarkAdded = true;
            log.debug("EasyExcel水印添加成功 [{}]，工作簿包含 {} 个工作表", 
                    stage, workbook.getNumberOfSheets());
        } catch (Exception e) {
            log.error("EasyExcel水印添加失败 [{}]", stage, e);
        }
    }

    /**
     * 为指定工作表添加水印
     */
    private void addWatermarkToSheet(Workbook workbook, Sheet sheet, String stage) {
        try {
            // 只为当前工作表添加水印
            ExcelWatermarkUtil.addWatermarkToSpecificSheet(workbook, sheet, watermarkConfig);
            log.debug("EasyExcel水印添加成功 [{}]，工作表: {}", stage, sheet.getSheetName());
        } catch (Exception e) {
            log.error("EasyExcel水印添加失败 [{}]，工作表: {}", stage, sheet.getSheetName(), e);
        }
    }

    /**
     * 使用回退策略添加水印
     * 当内容检测失败时，使用预估的内容区域
     */
    private void addWatermarkWithFallback(Workbook workbook, Sheet sheet) {
        try {
            // 使用回退策略：预估内容区域
            ExcelWatermarkUtil.addWatermarkWithFallback(workbook, sheet, watermarkConfig);
            log.debug("EasyExcel水印添加成功 [回退模式]，工作表: {}", sheet.getSheetName());
        } catch (Exception e) {
            log.error("EasyExcel水印添加失败 [回退模式]，工作表: {}", sheet.getSheetName(), e);
        }
    }

    // 静态工厂方法

    /**
     * 创建默认水印处理器（推荐）
     * 在工作簿写入完成后添加水印
     */
    public static EasyExcelWatermarkHandlerV2 create(String watermarkText) {
        return new EasyExcelWatermarkHandlerV2(watermarkText, WatermarkTiming.AFTER_WORKBOOK_DISPOSE);
    }

    /**
     * 创建工作表级别的水印处理器
     * 适用于多工作表场景
     */
    public static EasyExcelWatermarkHandlerV2 createForSheet(String watermarkText) {
        return new EasyExcelWatermarkHandlerV2(watermarkText, WatermarkTiming.AFTER_SHEET_DISPOSE);
    }

    /**
     * 创建兼容模式的水印处理器
     * 适用于模板导出等特殊场景
     */
    public static EasyExcelWatermarkHandlerV2 createFallback(String watermarkText) {
        return new EasyExcelWatermarkHandlerV2(watermarkText, WatermarkTiming.AFTER_SHEET_CREATE_FALLBACK);
    }

    /**
     * 创建自定义配置的水印处理器
     */
    public static EasyExcelWatermarkHandlerV2 createWithConfig(WatermarkConfig config, WatermarkTiming timing) {
        return new EasyExcelWatermarkHandlerV2(config, timing);
    }
}
