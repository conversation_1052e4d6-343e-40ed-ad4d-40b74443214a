package com.dcas.common.utils.excel;

import lombok.Builder;
import lombok.Data;

import java.awt.*;

/**
 * Excel水印配置类
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Data
@Builder
public class WatermarkConfig {

    /**
     * 水印文本内容
     */
    private String text;

    /**
     * 字体名称，默认为宋体
     */
    @Builder.Default
    private String fontName = "宋体";

    /**
     * 字体大小，默认为20
     */
    @Builder.Default
    private int fontSize = 20;

    /**
     * 字体颜色，默认为浅灰色
     */
    @Builder.Default
    private Color fontColor = new Color(50, 50, 50, 100);

    /**
     * 旋转角度（度），默认为-45度
     */
    @Builder.Default
    private double rotationAngle = -45.0;

    /**
     * 水印透明度 (0.0-1.0)，默认为0.3
     */
    @Builder.Default
    private float transparency = 0.5f;

    /**
     * 水印水平间距，默认为200像素
     */
    @Builder.Default
    private int horizontalSpacing = 200;

    /**
     * 水印垂直间距，默认为150像素
     */
    @Builder.Default
    private int verticalSpacing = 150;

    /**
     * 水印起始X坐标偏移，默认为50像素
     */
    @Builder.Default
    private int offsetX = 50;

    /**
     * 水印起始Y坐标偏移，默认为50像素
     */
    @Builder.Default
    private int offsetY = 50;

    /**
     * 是否加粗字体，默认为false
     */
    @Builder.Default
    private boolean bold = false;

    /**
     * 是否斜体字体，默认为false
     */
    @Builder.Default
    private boolean italic = false;

    /**
     * 创建默认配置
     *
     * @param text 水印文本
     * @return 默认配置
     */
    public static WatermarkConfig defaultConfig(String text) {
        return WatermarkConfig.builder()
                .text(text)
                .build();
    }

    /**
     * 创建简单配置
     *
     * @param text 水印文本
     * @param fontSize 字体大小
     * @param transparency 透明度
     * @return 简单配置
     */
    public static WatermarkConfig simpleConfig(String text, int fontSize, float transparency) {
        return WatermarkConfig.builder()
                .text(text)
                .fontSize(fontSize)
                .transparency(transparency)
                .build();
    }
}
