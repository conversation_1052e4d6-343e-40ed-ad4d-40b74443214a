package com.dcas.common.utils.excel;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.Color;
import java.awt.Font;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Excel水印工具类
 * 支持动态内容检测和精确水印定位
 *
 * <AUTHOR>
 * @since *******
 */
@Slf4j
public class ExcelWatermarkUtil {

    /**
     * 内容边界信息
     */
    @Data
    public static class ContentBounds {
        private int firstRow = -1;
        private int lastRow = -1;
        private int firstCol = -1;
        private int lastCol = -1;
        private int totalRows = 0;
        private int totalCols = 0;

        public boolean isEmpty() {
            return firstRow == -1 || lastRow == -1 || firstCol == -1 || lastCol == -1;
        }

        public int getRowCount() {
            return isEmpty() ? 0 : lastRow - firstRow + 1;
        }

        public int getColCount() {
            return isEmpty() ? 0 : lastCol - firstCol + 1;
        }
    }

    /**
     * 为工作簿添加水印
     *
     * @param workbook 工作簿
     * @param config 水印配置
     */
    public static void addWatermark(Workbook workbook, WatermarkConfig config) {
        if (workbook == null || config == null || config.getText() == null || config.getText().trim().isEmpty()) {
            log.warn("工作簿或水印配置为空，跳过水印添加");
            return;
        }

        try {
            // 为每个工作表添加水印
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                // 根据工作表大小生成水印图片并添加
                addWatermarkToSheet(workbook, sheet, config);
            }
            
            log.info("成功为工作簿添加水印，文本: {}", config.getText());
        } catch (Exception e) {
            log.error("添加水印失败", e);
        }
    }

    /**
     * 为指定工作表添加水印
     *
     * @param workbook 工作簿
     * @param sheet 工作表
     * @param config 水印配置
     */
    private static void addWatermarkToSheet(Workbook workbook, Sheet sheet, WatermarkConfig config) {
        try {
            // 检测实际内容区域
            ContentBounds bounds = detectContentBounds(sheet);
            if (bounds.isEmpty()) {
                log.debug("工作表 {} 没有内容，跳过水印添加", sheet.getSheetName());
                return;
            }

            // 根据内容区域生成适配的水印图片
            byte[] watermarkImage = createContentAwareWatermarkImage(config, bounds);

            // 添加图片到工作簿
            int pictureIdx = workbook.addPicture(watermarkImage, Workbook.PICTURE_TYPE_PNG);

            // 创建绘图对象
            Drawing<?> drawing = sheet.createDrawingPatriarch();

            // 创建精确的锚点（覆盖实际内容区域）
            ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
            anchor.setCol1(bounds.getFirstCol());
            anchor.setRow1(bounds.getFirstRow());
            anchor.setCol2(bounds.getLastCol() + 1); // +1 确保完全覆盖
            anchor.setRow2(bounds.getLastRow() + 1); // +1 确保完全覆盖

            // 设置锚点类型为不随单元格移动和调整大小
            anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);

            // 创建图片
            Picture picture = drawing.createPicture(anchor, pictureIdx);

            log.debug("成功为工作表 {} 添加水印，覆盖区域: 行{}-{}, 列{}-{}",
                    sheet.getSheetName(), bounds.getFirstRow(), bounds.getLastRow(),
                    bounds.getFirstCol(), bounds.getLastCol());

        } catch (Exception e) {
            log.error("为工作表 {} 添加水印失败", sheet.getSheetName(), e);
        }
    }

    /**
     * 检测工作表的实际内容边界
     *
     * @param sheet 工作表
     * @return 内容边界信息
     */
    private static ContentBounds detectContentBounds(Sheet sheet) {
        ContentBounds bounds = new ContentBounds();

        if (sheet == null) {
            return bounds;
        }

        int firstRowWithContent = -1;
        int lastRowWithContent = -1;
        int firstColWithContent = Integer.MAX_VALUE;
        int lastColWithContent = -1;

        // 遍历所有行，寻找有内容的行
        int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();
        if (physicalNumberOfRows == 0) {
            // 如果没有物理行，检查是否有合并单元格
            int numMergedRegions = sheet.getNumMergedRegions();
            if (numMergedRegions > 0) {
                // 有合并单元格，使用合并单元格的范围
                for (int i = 0; i < numMergedRegions; i++) {
                    CellRangeAddress mergedRegion = sheet.getMergedRegion(i);
                    if (firstRowWithContent == -1) {
                        firstRowWithContent = mergedRegion.getFirstRow();
                    }
                    lastRowWithContent = Math.max(lastRowWithContent, mergedRegion.getLastRow());
                    firstColWithContent = Math.min(firstColWithContent, mergedRegion.getFirstColumn());
                    lastColWithContent = Math.max(lastColWithContent, mergedRegion.getLastColumn());
                }
            } else {
                return bounds; // 完全空的工作表
            }
        } else {
            // 遍历所有物理行
            for (Row row : sheet) {
                if (row == null) continue;

                boolean rowHasContent = false;
                int rowFirstCol = Integer.MAX_VALUE;
                int rowLastCol = -1;

                // 检查行中的每个单元格
                for (Cell cell : row) {
                    if (cell == null) continue;

                    if (hasContent(cell)) {
                        rowHasContent = true;
                        rowFirstCol = Math.min(rowFirstCol, cell.getColumnIndex());
                        rowLastCol = Math.max(rowLastCol, cell.getColumnIndex());
                    }
                }

                // 如果行有内容，更新边界
                if (rowHasContent) {
                    if (firstRowWithContent == -1) {
                        firstRowWithContent = row.getRowNum();
                    }
                    lastRowWithContent = row.getRowNum();
                    firstColWithContent = Math.min(firstColWithContent, rowFirstCol);
                    lastColWithContent = Math.max(lastColWithContent, rowLastCol);
                }
            }
        }

        // 设置边界信息
        if (firstRowWithContent != -1 && lastRowWithContent != -1 &&
            firstColWithContent != Integer.MAX_VALUE && lastColWithContent != -1) {
            bounds.setFirstRow(firstRowWithContent);
            bounds.setLastRow(lastRowWithContent);
            bounds.setFirstCol(firstColWithContent);
            bounds.setLastCol(lastColWithContent);
            bounds.setTotalRows(lastRowWithContent - firstRowWithContent + 1);
            bounds.setTotalCols(lastColWithContent - firstColWithContent + 1);
        }

        log.debug("检测到工作表 {} 的内容边界: 行{}-{}, 列{}-{}",
                sheet.getSheetName(), bounds.getFirstRow(), bounds.getLastRow(),
                bounds.getFirstCol(), bounds.getLastCol());

        return bounds;
    }

    /**
     * 检查单元格是否有内容
     *
     * @param cell 单元格
     * @return 是否有内容
     */
    private static boolean hasContent(Cell cell) {
        if (cell == null) {
            return false;
        }

        CellType cellType = cell.getCellType();
        switch (cellType) {
            case STRING:
                String stringValue = cell.getStringCellValue();
                return stringValue != null && !stringValue.trim().isEmpty();
            case NUMERIC:
                return true; // 数字总是有内容
            case BOOLEAN:
                return true; // 布尔值总是有内容
            case FORMULA:
                return true; // 公式总是有内容
            case ERROR:
                return true; // 错误值也算有内容
            case BLANK:
            case _NONE:
            default:
                return false;
        }
    }

    /**
     * 创建内容感知的水印图片
     *
     * @param config 水印配置
     * @param bounds 内容边界
     * @return 水印图片字节数组
     * @throws IOException IO异常
     */
    private static byte[] createContentAwareWatermarkImage(WatermarkConfig config, ContentBounds bounds) throws IOException {
        // 根据内容区域计算图片尺寸
        // Excel列宽约75像素，行高约20像素（估算值）
        int imageWidth = Math.max(bounds.getColCount() * 75, 400);
        int imageHeight = Math.max(bounds.getRowCount() * 20, 300);

        return createWatermarkImageWithSize(config, imageWidth, imageHeight);
    }

    /**
     * 创建指定尺寸的水印图片
     *
     * @param config 水印配置
     * @param imageWidth 图片宽度
     * @param imageHeight 图片高度
     * @return 水印图片字节数组
     * @throws IOException IO异常
     */
    private static byte[] createWatermarkImageWithSize(WatermarkConfig config, int imageWidth, int imageHeight) throws IOException {
        // 创建透明背景的图片
        BufferedImage image = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        try {
            // 设置透明背景
            g2d.setComposite(AlphaComposite.Clear);
            g2d.fillRect(0, 0, imageWidth, imageHeight);
            
            // 设置渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            // 设置字体
            int fontStyle = Font.PLAIN;
            if (config.isBold()) fontStyle |= Font.BOLD;
            if (config.isItalic()) fontStyle |= Font.ITALIC;
            
            Font font = new Font(config.getFontName(), fontStyle, config.getFontSize());
            g2d.setFont(font);
            
            // 设置颜色和透明度
            Color color = config.getFontColor();
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, config.getTransparency()));
            g2d.setColor(color);
            
            // 获取文本尺寸
            FontRenderContext frc = g2d.getFontRenderContext();
            Rectangle2D textBounds = font.getStringBounds(config.getText(), frc);
            int textWidth = (int) textBounds.getWidth();
            int textHeight = (int) textBounds.getHeight();
            
            // 根据图片大小自适应调整水印间距和密度
            int adaptiveHorizontalSpacing = calculateAdaptiveSpacing(config.getHorizontalSpacing(), imageWidth, textWidth);
            int adaptiveVerticalSpacing = calculateAdaptiveSpacing(config.getVerticalSpacing(), imageHeight, textHeight);

            // 计算起始偏移，确保水印居中分布
            int startX = Math.max(config.getOffsetX(), adaptiveHorizontalSpacing / 2);
            int startY = Math.max(config.getOffsetY(), adaptiveVerticalSpacing / 2);

            // 计算旋转后的文本位置并绘制多个水印
            AffineTransform originalTransform = g2d.getTransform();

            for (int x = startX; x < imageWidth; x += adaptiveHorizontalSpacing) {
                for (int y = startY; y < imageHeight; y += adaptiveVerticalSpacing) {
                    // 保存当前变换
                    g2d.setTransform(originalTransform);

                    // 移动到绘制位置
                    g2d.translate(x, y);

                    // 旋转
                    g2d.rotate(Math.toRadians(config.getRotationAngle()));

                    // 绘制文本
                    g2d.drawString(config.getText(), -textWidth / 2, textHeight / 2);
                }
            }
            
        } finally {
            g2d.dispose();
        }
        
        // 将图片转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        return baos.toByteArray();
    }

    /**
     * 计算自适应间距
     *
     * @param configSpacing 配置的间距
     * @param imageSize 图片尺寸（宽度或高度）
     * @param textSize 文本尺寸（宽度或高度）
     * @return 自适应间距
     */
    private static int calculateAdaptiveSpacing(int configSpacing, int imageSize, int textSize) {
        // 确保间距不小于文本尺寸的1.5倍，不大于图片尺寸的1/3
        int minSpacing = (int) (textSize * 1.5);
        int maxSpacing = imageSize / 3;

        // 如果配置的间距合理，使用配置值；否则使用自适应值
        if (configSpacing >= minSpacing && configSpacing <= maxSpacing) {
            return configSpacing;
        } else {
            // 根据图片大小自动计算合适的间距
            return Math.max(minSpacing, Math.min(maxSpacing, imageSize / 5));
        }
    }

    /**
     * 为工作簿添加简单水印
     *
     * @param workbook 工作簿
     * @param watermarkText 水印文本
     */
    public static void addSimpleWatermark(Workbook workbook, String watermarkText) {
        WatermarkConfig config = WatermarkConfig.defaultConfig(watermarkText);
        addWatermark(workbook, config);
    }

    /**
     * 为工作簿添加自定义水印
     *
     * @param workbook 工作簿
     * @param watermarkText 水印文本
     * @param fontSize 字体大小
     * @param transparency 透明度
     */
    public static void addCustomWatermark(Workbook workbook, String watermarkText, int fontSize, float transparency) {
        WatermarkConfig config = WatermarkConfig.simpleConfig(watermarkText, fontSize, transparency);
        addWatermark(workbook, config);
    }
}
