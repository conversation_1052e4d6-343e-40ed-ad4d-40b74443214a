package com.dcas.common.utils.excel;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.Color;
import java.awt.Font;
import java.awt.font.FontRenderContext;
import java.awt.geom.AffineTransform;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * Excel水印工具类
 * 
 * <AUTHOR>
 * @since 2.1.3.0
 */
@Slf4j
public class ExcelWatermarkUtil {

    /**
     * 为工作簿添加水印
     *
     * @param workbook 工作簿
     * @param config 水印配置
     */
    public static void addWatermark(Workbook workbook, WatermarkConfig config) {
        if (workbook == null || config == null || config.getText() == null || config.getText().trim().isEmpty()) {
            log.warn("工作簿或水印配置为空，跳过水印添加");
            return;
        }

        try {
            // 为每个工作表添加水印
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                // 根据工作表大小生成水印图片并添加
                addWatermarkToSheet(workbook, sheet, config);
            }
            
            log.info("成功为工作簿添加水印，文本: {}", config.getText());
        } catch (Exception e) {
            log.error("添加水印失败", e);
        }
    }

    /**
     * 为指定工作表添加水印
     *
     * @param workbook 工作簿
     * @param sheet 工作表
     * @param config 水印配置
     */
    private static void addWatermarkToSheet(Workbook workbook, Sheet sheet, WatermarkConfig config) {
        try {
            // 计算工作表的实际大小
            int lastRowNum = Math.max(sheet.getLastRowNum(), 100); // 至少100行
            int lastColNum = 0;
            
            // 获取最大列数
            for (int i = 0; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    lastColNum = Math.max(lastColNum, row.getLastCellNum());
                }
            }
            lastColNum = Math.max(lastColNum, 20); // 至少20列
            
            // 根据工作表大小生成水印图片
            byte[] watermarkImage = createWatermarkImage(config, lastColNum, lastRowNum);
            
            // 添加图片到工作簿
            int pictureIdx = workbook.addPicture(watermarkImage, Workbook.PICTURE_TYPE_PNG);
            
            // 创建绘图对象
            Drawing<?> drawing = sheet.createDrawingPatriarch();
            
            // 创建锚点（图片位置）
            ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
            anchor.setCol1(0);
            anchor.setRow1(0);
            anchor.setCol2(lastColNum);
            anchor.setRow2(lastRowNum);
            
            // 创建图片
            Picture picture = drawing.createPicture(anchor, pictureIdx);
            
            // 调整图片大小以覆盖整个工作表
            picture.resize();
            
        } catch (Exception e) {
            log.error("为工作表 {} 添加水印失败", sheet.getSheetName(), e);
        }
    }

    /**
     * 创建水印图片
     *
     * @param config 水印配置
     * @param colCount 列数
     * @param rowCount 行数
     * @return 水印图片字节数组
     * @throws IOException IO异常
     */
    private static byte[] createWatermarkImage(WatermarkConfig config, int colCount, int rowCount) throws IOException {
        // 根据工作表大小动态计算图片尺寸
        // 每列约75像素宽，每行约20像素高（估算值）
        int imageWidth = Math.max(colCount * 75, 800);
        int imageHeight = Math.max(rowCount * 20, 600);
        
        // 创建透明背景的图片
        BufferedImage image = new BufferedImage(imageWidth, imageHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = image.createGraphics();
        
        try {
            // 设置透明背景
            g2d.setComposite(AlphaComposite.Clear);
            g2d.fillRect(0, 0, imageWidth, imageHeight);
            
            // 设置渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            // 设置字体
            int fontStyle = Font.PLAIN;
            if (config.isBold()) fontStyle |= Font.BOLD;
            if (config.isItalic()) fontStyle |= Font.ITALIC;
            
            Font font = new Font(config.getFontName(), fontStyle, config.getFontSize());
            g2d.setFont(font);
            
            // 设置颜色和透明度
            Color color = config.getFontColor();
            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, config.getTransparency()));
            g2d.setColor(color);
            
            // 获取文本尺寸
            FontRenderContext frc = g2d.getFontRenderContext();
            Rectangle2D textBounds = font.getStringBounds(config.getText(), frc);
            int textWidth = (int) textBounds.getWidth();
            int textHeight = (int) textBounds.getHeight();
            
            // 根据图片大小调整水印间距
            int horizontalSpacing = Math.min(config.getHorizontalSpacing(), imageWidth / 4);
            int verticalSpacing = Math.min(config.getVerticalSpacing(), imageHeight / 4);
            
            // 计算旋转后的文本位置并绘制多个水印
            AffineTransform originalTransform = g2d.getTransform();
            
            for (int x = config.getOffsetX(); x < imageWidth; x += horizontalSpacing) {
                for (int y = config.getOffsetY(); y < imageHeight; y += verticalSpacing) {
                    // 保存当前变换
                    g2d.setTransform(originalTransform);
                    
                    // 移动到绘制位置
                    g2d.translate(x, y);
                    
                    // 旋转
                    g2d.rotate(Math.toRadians(config.getRotationAngle()));
                    
                    // 绘制文本
                    g2d.drawString(config.getText(), -textWidth / 2, textHeight / 2);
                }
            }
            
        } finally {
            g2d.dispose();
        }
        
        // 将图片转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "PNG", baos);
        return baos.toByteArray();
    }

    /**
     * 为工作簿添加简单水印
     *
     * @param workbook 工作簿
     * @param watermarkText 水印文本
     */
    public static void addSimpleWatermark(Workbook workbook, String watermarkText) {
        WatermarkConfig config = WatermarkConfig.defaultConfig(watermarkText);
        addWatermark(workbook, config);
    }

    /**
     * 为工作簿添加自定义水印
     *
     * @param workbook 工作簿
     * @param watermarkText 水印文本
     * @param fontSize 字体大小
     * @param transparency 透明度
     */
    public static void addCustomWatermark(Workbook workbook, String watermarkText, int fontSize, float transparency) {
        WatermarkConfig config = WatermarkConfig.simpleConfig(watermarkText, fontSize, transparency);
        addWatermark(workbook, config);
    }
}
