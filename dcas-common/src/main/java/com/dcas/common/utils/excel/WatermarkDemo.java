package com.dcas.common.utils.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.dcas.common.utils.Func;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Excel水印功能演示类
 * 展示如何在实际项目中使用水印功能
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
public class WatermarkDemo {

    /**
     * 演示数据类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeData {
        @ExcelProperty("员工编号")
        private String employeeId;
        
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("部门")
        private String department;
        
        @ExcelProperty("职位")
        private String position;
        
        @ExcelProperty("薪资")
        private Double salary;
    }

    /**
     * 演示1: 使用Func.export方法导出带水印的Excel
     * 这是最简单的集成方式
     */
    public static void demoFuncExportWithWatermark(HttpServletResponse response) {
        try {
            // 准备演示数据
            List<EmployeeData> employees = Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0),
                    new EmployeeData("E003", "王五", "人事部", "人事专员", 8000.0)
            );

            // 使用扩展的Func.export方法，直接添加水印
            Func.export(response, employees, "员工信息导出", "机密文档");
            
            log.info("成功导出带水印的员工信息Excel文件");
        } catch (IOException e) {
            log.error("导出Excel文件失败", e);
        }
    }

    /**
     * 演示2: 使用Hutool ExcelWriter手动添加水印
     * 适用于需要更多控制的场景
     */
    public static void demoHutoolExcelWriterWithWatermark(String outputPath) {
        try {
            // 准备演示数据
            List<EmployeeData> employees = Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0),
                    new EmployeeData("E003", "王五", "人事部", "人事专员", 8000.0)
            );

            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(employees, true);
                
                // 添加自定义水印
                WatermarkConfig config = WatermarkConfig.builder()
                        .text("内部使用")
                        .fontSize(22)
                        .transparency(0.25f)
                        .rotationAngle(-35.0)
                        .build();
                
                ExcelWatermarkUtil.addWatermark(writer.getWorkbook(), config);
                
                // 保存文件
                try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                    writer.flush(fos, true);
                }
            }
            
            log.info("成功生成带自定义水印的Excel文件: {}", outputPath);
        } catch (IOException e) {
            log.error("生成Excel文件失败", e);
        }
    }

    /**
     * 演示3: 使用EasyExcel导出带水印的Excel
     * 适用于使用EasyExcel的场景
     */
    public static void demoEasyExcelWithWatermark(String outputPath) {
        try {
            // 准备演示数据
            List<EmployeeData> employees = Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0),
                    new EmployeeData("E003", "王五", "人事部", "人事专员", 8000.0)
            );

            // 使用EasyExcel导出带水印的文件
            EasyExcel.write(outputPath, EmployeeData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple("EasyExcel水印"))
                    .sheet("员工数据")
                    .doWrite(employees);
            
            log.info("成功使用EasyExcel生成带水印的Excel文件: {}", outputPath);
        } catch (Exception e) {
            log.error("EasyExcel导出失败", e);
        }
    }

    /**
     * 演示4: 根据数据敏感级别选择不同的水印
     */
    public static void demoSensitivityBasedWatermark(HttpServletResponse response, int sensitivityLevel) {
        try {
            // 准备演示数据
            List<EmployeeData> employees = Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0)
            );

            String watermarkText;
            WatermarkConfig customConfig = null;

            switch (sensitivityLevel) {
                case 1: // 公开
                    watermarkText = null; // 不添加水印
                    break;
                case 2: // 内部
                    watermarkText = "内部使用";
                    break;
                case 3: // 机密
                    watermarkText = "机密文档";
                    break;
                case 4: // 绝密
                    customConfig = WatermarkConfig.builder()
                            .text("绝密文档")
                            .fontSize(28)
                            .transparency(0.15f)
                            .fontColor(new java.awt.Color(255, 0, 0, 120))
                            .bold(true)
                            .build();
                    break;
                default:
                    watermarkText = "内部使用";
            }

            if (customConfig != null) {
                // 使用自定义配置
                try (ExcelWriter writer = ExcelUtil.getWriter()) {
                    writer.setOnlyAlias(true);
                    writer.write(employees, true);
                    ExcelWatermarkUtil.addWatermark(writer.getWorkbook(), customConfig);
                    Func.responseSetting(response, "绝密员工信息.xlsx");
                    writer.flush(response.getOutputStream(), true);
                }
            } else {
                // 使用简单配置或不添加水印
                Func.export(response, employees, "员工信息", watermarkText);
            }
            
            log.info("成功导出敏感级别{}的员工信息", sensitivityLevel);
        } catch (IOException e) {
            log.error("导出敏感数据失败", e);
        }
    }

    /**
     * 演示5: 批量导出多个带水印的文件
     */
    public static void demoBatchExportWithWatermark(String outputDir) {
        // 不同部门的数据
        List<EmployeeData> techDept = Arrays.asList(
                new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                new EmployeeData("E004", "赵六", "技术部", "架构师", 20000.0)
        );

        List<EmployeeData> salesDept = Arrays.asList(
                new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0),
                new EmployeeData("E005", "钱七", "销售部", "销售代表", 8000.0)
        );

        List<EmployeeData> hrDept = Arrays.asList(
                new EmployeeData("E003", "王五", "人事部", "人事专员", 8000.0),
                new EmployeeData("E006", "孙八", "人事部", "招聘专员", 7000.0)
        );

        // 批量导出
        exportDepartmentData(outputDir + "/技术部员工.xlsx", techDept, "技术部专用");
        exportDepartmentData(outputDir + "/销售部员工.xlsx", salesDept, "销售部专用");
        exportDepartmentData(outputDir + "/人事部员工.xlsx", hrDept, "人事部专用");
        
        log.info("成功批量导出所有部门的带水印Excel文件");
    }

    /**
     * 辅助方法：导出部门数据
     */
    private static void exportDepartmentData(String filePath, List<EmployeeData> data, String watermark) {
        try {
            EasyExcel.write(filePath, EmployeeData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple(watermark))
                    .sheet("部门员工")
                    .doWrite(data);
            log.info("成功导出部门文件: {}", filePath);
        } catch (Exception e) {
            log.error("导出部门文件失败: {}", filePath, e);
        }
    }

    /**
     * 演示6: 现有代码的最小化修改示例
     */
    public static class ExistingCodeModificationDemo {
        
        /**
         * 原有的导出方法（模拟现有代码）
         */
        public void originalExportMethod(HttpServletResponse response) throws IOException {
            List<EmployeeData> data = getEmployeeData();
            
            // 原有代码
            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(data, true);
                Func.responseSetting(response, "员工信息.xlsx");
                writer.flush(response.getOutputStream(), true);
            }
        }

        /**
         * 修改后的导出方法（添加水印）
         * 只需要添加一行代码
         */
        public void modifiedExportMethod(HttpServletResponse response) throws IOException {
            List<EmployeeData> data = getEmployeeData();
            
            // 修改后的代码
            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(data, true);
                
                // 只需要添加这一行代码即可集成水印功能
                ExcelWatermarkUtil.addSimpleWatermark(writer.getWorkbook(), "内部使用");
                
                Func.responseSetting(response, "员工信息.xlsx");
                writer.flush(response.getOutputStream(), true);
            }
        }

        /**
         * 使用Func.export的简化版本
         */
        public void simplifiedExportMethod(HttpServletResponse response) throws IOException {
            List<EmployeeData> data = getEmployeeData();
            
            // 最简单的修改方式
            Func.export(response, data, "员工信息", "内部使用");
        }

        private List<EmployeeData> getEmployeeData() {
            return Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0)
            );
        }
    }

    /**
     * 主方法：运行所有演示
     */
    public static void runAllDemos() {
        log.info("开始运行Excel水印功能演示...");
        
        try {
            // 演示文件输出目录
            String outputDir = "demo_output";
            
            // 演示2: Hutool ExcelWriter
            demoHutoolExcelWriterWithWatermark(outputDir + "/hutool_watermark_demo.xlsx");
            
            // 演示3: EasyExcel
            demoEasyExcelWithWatermark(outputDir + "/easyexcel_watermark_demo.xlsx");
            
            // 演示5: 批量导出
            demoBatchExportWithWatermark(outputDir);
            
            log.info("所有演示完成！");
        } catch (Exception e) {
            log.error("演示过程中发生错误", e);
        }
    }
}
