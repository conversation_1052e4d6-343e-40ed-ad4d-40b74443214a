ackage com.dcas.common.utils.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * 改进版Excel水印功能演示
 * 展示动态内容检测和精确水印定位功能
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
@Slf4j
public class ImprovedWatermarkDemo {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeData {
        @ExcelProperty("员工编号")
        private String employeeId;
        
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("部门")
        private String department;
        
        @ExcelProperty("职位")
        private String position;
        
        @ExcelProperty("薪资")
        private Double salary;
    }

    /**
     * 演示1: 内容检测功能
     * 展示水印如何根据实际内容区域进行精确定位
     */
    public static void demoContentDetection(String outputPath) {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("员工信息");
            
            // 添加标题行（从第1行开始）
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("员工编号");
            headerRow.createCell(1).setCellValue("姓名");
            headerRow.createCell(2).setCellValue("部门");
            headerRow.createCell(3).setCellValue("职位");
            headerRow.createCell(4).setCellValue("薪资");
            
            // 添加数据行
            String[][] data = {
                {"E001", "张三", "技术部", "高级工程师", "15000"},
                {"E002", "李四", "销售部", "销售经理", "12000"},
                {"E003", "王五", "人事部", "人事专员", "8000"}
            };
            
            for (int i = 0; i < data.length; i++) {
                Row dataRow = sheet.createRow(i + 1);
                for (int j = 0; j < data[i].length; j++) {
                    Cell cell = dataRow.createCell(j);
                    if (j == 4) { // 薪资列
                        cell.setCellValue(Double.parseDouble(data[i][j]));
                    } else {
                        cell.setCellValue(data[i][j]);
                    }
                }
            }
            
            // 添加水印 - 水印将精确覆盖实际内容区域（行0-3，列0-4）
            ExcelWatermarkUtil.addSimpleWatermark(workbook, "机密文档");
            
            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                workbook.write(fos);
            }
            workbook.close();
            
            log.info("成功生成内容检测演示文件: {}", outputPath);
        } catch (IOException e) {
            log.error("生成内容检测演示文件失败", e);
        }
    }

    /**
     * 演示2: 不规则内容区域处理
     * 展示水印如何处理分散的内容区域
     */
    public static void demoIrregularContent(String outputPath) {
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("不规则内容");
            
            // 在不连续的位置添加内容
            sheet.createRow(0).createCell(0).setCellValue("标题");
            sheet.createRow(2).createCell(2).setCellValue("数据1");
            sheet.createRow(5).createCell(1).setCellValue("数据2");
            sheet.createRow(8).createCell(4).setCellValue("数据3");
            sheet.createRow(10).createCell(3).setCellValue("总计");
            
            // 添加水印 - 水印将覆盖从行0到行10，列0到列4的整个区域
            WatermarkConfig config = WatermarkConfig.builder()
                    .text("内部使用")
                    .fontSize(16)
                    .transparency(0.4f)
                    .rotationAngle(-30.0)
                    .build();
            
            ExcelWatermarkUtil.addWatermark(workbook, config);
            
            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                workbook.write(fos);
            }
            workbook.close();
            
            log.info("成功生成不规则内容演示文件: {}", outputPath);
        } catch (IOException e) {
            log.error("生成不规则内容演示文件失败", e);
        }
    }

    /**
     * 演示3: 空工作表处理
     * 展示水印如何处理空工作表
     */
    public static void demoEmptySheetHandling(String outputPath) {
        try {
            Workbook workbook = new XSSFWorkbook();
            
            // 创建一个空工作表
            Sheet emptySheet = workbook.createSheet("空工作表");
            
            // 创建一个有内容的工作表
            Sheet contentSheet = workbook.createSheet("有内容的工作表");
            Row row = contentSheet.createRow(0);
            row.createCell(0).setCellValue("这是有内容的工作表");
            
            // 添加水印 - 只会在有内容的工作表上添加水印
            ExcelWatermarkUtil.addSimpleWatermark(workbook, "测试水印");
            
            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                workbook.write(fos);
            }
            workbook.close();
            
            log.info("成功生成空工作表处理演示文件: {}", outputPath);
        } catch (IOException e) {
            log.error("生成空工作表处理演示文件失败", e);
        }
    }

    /**
     * 演示4: 大数据集水印适配
     * 展示水印如何适配大量数据的工作表
     */
    public static void demoLargeDatasetWatermark(String outputPath) {
        try {
            // 使用Hutool ExcelWriter创建大数据集
            List<EmployeeData> largeDataset = Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0),
                    new EmployeeData("E003", "王五", "人事部", "人事专员", 8000.0),
                    new EmployeeData("E004", "赵六", "财务部", "会计师", 10000.0),
                    new EmployeeData("E005", "钱七", "技术部", "架构师", 20000.0),
                    new EmployeeData("E006", "孙八", "市场部", "市场专员", 9000.0),
                    new EmployeeData("E007", "周九", "运营部", "运营经理", 13000.0),
                    new EmployeeData("E008", "吴十", "客服部", "客服主管", 8500.0)
            );

            try (ExcelWriter writer = ExcelUtil.getWriter()) {
                writer.setOnlyAlias(true);
                writer.write(largeDataset, true);
                
                // 添加自适应水印 - 水印密度和间距会根据数据量自动调整
                WatermarkConfig config = WatermarkConfig.builder()
                        .text("大数据集测试")
                        .fontSize(18)
                        .transparency(0.3f)
                        .horizontalSpacing(150) // 这个值会根据实际内容大小自动调整
                        .verticalSpacing(100)   // 这个值会根据实际内容大小自动调整
                        .build();
                
                ExcelWatermarkUtil.addWatermark(writer.getWorkbook(), config);
                
                // 保存文件
                try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                    writer.flush(fos, true);
                }
            }
            
            log.info("成功生成大数据集水印演示文件: {}", outputPath);
        } catch (IOException e) {
            log.error("生成大数据集水印演示文件失败", e);
        }
    }

    /**
     * 演示5: EasyExcel集成的内容感知水印
     */
    public static void demoEasyExcelContentAware(String outputPath) {
        try {
            List<EmployeeData> employees = Arrays.asList(
                    new EmployeeData("E001", "张三", "技术部", "高级工程师", 15000.0),
                    new EmployeeData("E002", "李四", "销售部", "销售经理", 12000.0),
                    new EmployeeData("E003", "王五", "人事部", "人事专员", 8000.0)
            );

            // 使用改进的EasyExcel水印处理器
            EasyExcel.write(outputPath, EmployeeData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple("EasyExcel内容感知"))
                    .sheet("员工数据")
                    .doWrite(employees);
            
            log.info("成功生成EasyExcel内容感知水印演示文件: {}", outputPath);
        } catch (Exception e) {
            log.error("生成EasyExcel内容感知水印演示文件失败", e);
        }
    }

    /**
     * 运行所有改进版演示
     */
    public static void runAllImprovedDemos() {
        log.info("开始运行改进版Excel水印功能演示...");
        
        String outputDir = "improved_demo_output";
        
        try {
            // 演示1: 内容检测
            demoContentDetection(outputDir + "/content_detection_demo.xlsx");
            
            // 演示2: 不规则内容
            demoIrregularContent(outputDir + "/irregular_content_demo.xlsx");
            
            // 演示3: 空工作表处理
            demoEmptySheetHandling(outputDir + "/empty_sheet_demo.xlsx");
            
            // 演示4: 大数据集
            demoLargeDatasetWatermark(outputDir + "/large_dataset_demo.xlsx");
            
            // 演示5: EasyExcel集成
            demoEasyExcelContentAware(outputDir + "/easyexcel_content_aware_demo.xlsx");
            
            log.info("所有改进版演示完成！");
            log.info("改进功能包括：");
            log.info("1. 动态内容检测 - 自动识别实际数据区域");
            log.info("2. 精确水印定位 - 水印只覆盖有内容的区域");
            log.info("3. 自适应间距调整 - 根据内容大小调整水印密度");
            log.info("4. 空工作表处理 - 智能跳过空工作表");
            log.info("5. 不规则内容支持 - 正确处理分散的数据区域");
        } catch (Exception e) {
            log.error("改进版演示过程中发生错误", e);
        }
    }
}
