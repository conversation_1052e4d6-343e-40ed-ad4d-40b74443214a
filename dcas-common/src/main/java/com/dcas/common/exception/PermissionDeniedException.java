package com.dcas.common.exception;

import com.dcas.common.enums.CommonResultCode;
import lombok.Getter;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/4/1 15:21
 * @since 1.0.0
 */
@Getter
public class PermissionDeniedException extends RuntimeException{
    private static final long serialVersionUID = -1219416021834358396L;
    /**
     * 异常码
     */
    private final Integer code;

    /**
     * 错误信息
     */
    private final String msg;

    public PermissionDeniedException(String message) {
        super(message);
        this.code = CommonResultCode.UNAUTHORIZED.getCode();
        this.msg = CommonResultCode.UNAUTHORIZED.getMsg();
    }

    public PermissionDeniedException(Integer code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

}
