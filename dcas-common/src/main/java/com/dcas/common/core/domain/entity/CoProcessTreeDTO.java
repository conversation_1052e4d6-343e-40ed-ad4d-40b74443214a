package com.dcas.common.core.domain.entity;

import com.dcas.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 过程清单分类树入参
 *
 * <AUTHOR>
 * @Date 2022/5/26 11:58
 * @ClassName ConTreeDTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CoProcessTreeDTO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 分类树id
     */
    private Long treeId;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 分类树名称
     */
    private String treeName;

    /**
     * 显示顺序
     */
    private Object orderNum;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 父名称
     */
    private String parentName;

    /**
     * 子名称
     */
    private List<CoProcessTreeDTO> children;
}
