package com.dcas.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @className SummaryTypeEnum
 * @description 统计表类型
 * @date 2025/02/28 18:08
 */
@Getter
public enum SummaryTypeEnum {
    /**
     * 统计类型：1-网络资产，2-风险API，3-风险模型
     */
    NETWORK_ASSET(1, "网络资产"),
    RISK_API(2, "风险API"),
    RISK_MODEL(3, "风险模型"),
    ITEM_ABILITY(4, "核查项能力"),
    ITEM_ABILITY_COUNT(5, "核查项能力符合情况统计");

    private final int type;
    private final String desc;
    SummaryTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
