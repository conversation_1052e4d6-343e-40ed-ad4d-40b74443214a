package com.dcas.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 *  权限枚举，需要注意菜单出现的先后顺序
 *  枚举名称编写规则：
 *  一级菜单以FATHER开头，下划线连接页面路由id
 *  二级及其子菜单以CHILD开头，下划线连接父页面到当前子页面路由id；当前页面的操作按钮以当前页名称加上小写英文字母结尾。
 * </p>
 *
 * <AUTHOR>
 * @date 2024/06/14 14:31
 * @since 1.6.6
 */
@Getter
@AllArgsConstructor
public enum AuthType {

    /**
     * 工作台
     */
    FATHER_0("workbench", 0, "工作台", null, true),
    CHILD_0_a("workbench/page", null, "页面", "workbench", true),

    /**
     * 客户视图
     */
    FATHER_1("screen", 1, "数据大屏", null, true),
    CHILD_1_a("screen/page", null, "页面", "screen", true),


    /**
     * 能力市场
     */
    FATHER_6("market", 6, "能力市场", null, true),
    CHILD_6_a("market/page", null, "页面", "market", true),

    /**
     * 评估管理
     */
    FATHER_2("access", 2, "评估管理", null, true),
    CHILD_2_1("access/project", 1, "项目管理", "access", true),
    CHILD_2_1_a("access/project_page", null, "页面", "access/project", true),
    CHILD_2_2("access/work", 2, "评估作业", "access", true),
    CHILD_2_2_a("access/work_page", null, "页面", "access/work", true),
    CHILD_2_2_b("access/work_report", null, "报告", "access/work", true),
    CHILD_2_3("access/security", 3, "检查作业", "access", true),
    CHILD_2_3_a("access/security_page", null, "页面", "access/security", true),
    CHILD_2_3_b("access/security_report", null, "报告", "access/security", true),
    CHILD_2_4("access/source", 4, "数源管理", "access", true),
    CHILD_2_4_a("access/source_page", null, "页面", "access/source", true),
    CHILD_2_5("access/ai", 5, "智能引擎", "access", true),
    CHILD_2_5_a("access/ai_page", null, "页面", "access/ai", true),

    /**
     * 规则管理
     */
    FATHER_3("rule", 3, "规则管理", null, true),
    CHILD_3_1("rule/template", 1, "知识库模板", "rule", true),
    CHILD_3_1_a("rule/template_page", null, "页面", "rule/template", true),

    /**
     * 审计管理
     */
    FATHER_4("audit", 4, "审计管理", null, true),
    CHILD_4_1("audit/login", 1, "登录日志", "audit", true),
    CHILD_4_1_a("audit/login_page", null, "页面", "audit/login", true),
    CHILD_4_2("audit/system", 2, "系统日志", "audit", true),
    CHILD_4_2_a("audit/system_page", null, "页面", "audit/system", true),
    CHILD_4_3("audit/operate", 3, "操作日志", "audit", true),
    CHILD_4_3_a("audit/operate_page", null, "页面", "audit/operate", true),

    /**
     * 产品对接管理
     */
    FATHER_7("product", 7, "产品对接管理", null, true),
    CHILD_7_a("product_page", null, "页面", "product", true),

    /**
     * 系统管理
     */
    FATHER_5("system", 5, "系统管理", null, true),
    FATHER_5_1("system/maintain", 1, "运维管理", "system", true),
    FATHER_5_1_a("system/maintain_page", null, "页面", "system/maintain", true),
    FATHER_5_2("system/message", 2, "系统消息", "system", true),
    FATHER_5_2_a("system/message_page", null, "页面", "system/message", true),
    FATHER_5_3("system/empower", 3, "授权管理", "system", true),
    FATHER_5_3_a("system/empower_page", null, "页面", "system/empower", true),
    FATHER_5_4("system/upgrade", 4, "系统升级", "system", true),
    FATHER_5_4_a("system/upgrade_page", null, "页面", "system/upgrade", true)

    ;
    public static final Map<String, Integer> MAP = new HashMap<>();


    static {
        for (AuthType a : AuthType.values()) {
            Assert.notNull(a.getCode(), "权限代码不能为空");
            MAP.put(a.getCode(), a.getRouteId());
        }
    }

    public static String ofRouteId(String code) {
        Integer routeId = MAP.get(code);
        return Objects.isNull(routeId) ? StrUtil.EMPTY : routeId.toString();
    }

    /**
     * 自定义唯一code
     */
    private final String code;

    /**
     * 前端提供的页面路由id {page required}
     */
    private final Integer routeId;

    /**
     * 菜单名或权限名
     */
    private final String name;

    /**
     * 父级code
     */
    private final String parentCode;

    /**
     * 是否需要注册到统一身份
     */
    private final boolean register;
}
