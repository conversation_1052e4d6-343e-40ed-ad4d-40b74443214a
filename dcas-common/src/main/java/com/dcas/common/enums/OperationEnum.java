package com.dcas.common.enums;

import lombok.Getter;

/**
 * 操作符枚举
 *
 * <AUTHOR>
 * @date 2023/08/24 11:02
 **/
@Getter
public enum OperationEnum {
    /**
     * ×÷⌈⌉⌊⌋≈|||μΣ=√^∩∪
     */
    ADD(11L, "+","加法"),
    SUBTRACT(12L, "-","减法"),
    MULTIPLY(13L, "×","乘法"),
    DIVIDE(14L, "÷","除法"),
    UP(15L, "⌈⌉","向上取整,例：⌈⌉(0.1)"),
    DOWN(16L, "⌊⌋","向下取整,例：⌊⌋(0.1)"),
    ROUND(17L, "≈","四舍五入取整"),
    ABS(18L, "||","绝对值"),
    FULL_DIVIDE(19L, "|","整除"),
    AVG(20L, "μ","求平均值"),
    SUM(21L, "Σ","求和"),
    EQUALS(22L, "=","等号"),
    LEFT_BRACKET(23L, "(","左括号"),
    RIGHT_BRACKET(28L, ")","右括号"),
    SQRT(24L, "√", "根运算"),
    POW(25L, "^","幂运算"),
    INTERSECTION(26L, "∩","交集"),
    UNION(27L, "∪","并集"),
    COUNT(29L, "CT","求数量"),
    MAX(30L, "MAX","取最大值,例：MAX(指标1，指标2...) 或者 MAX([1,2])"),
    FILTER(31L, "FT","筛选"),
    IFS(32L, "IFS","逻辑判断ifs"),
    AND(33L, "AND","且运算"),
    OR(34L, "OR","或运算"),
    COMM(35L, ",","逗号"),
    GE(36L, "≥","大于等于，搭配IFS使用：例：IFS(A ≥ 1,1,0)"),
    LE(37L, "≤","小于等于，搭配IFS使用：例：IFS(A ≤ 1,1,0)"),
    EQ(38L, "==","等于，搭配IFS使用：例：IFS(A == 1,1,0)"),
    IN(39L, "IN","命中取值,（按顺序匹配，命中后停止）（值支持多类型，不一定为数值）,搭配结果判断使用 例：IN(指标1，指标2...)，结果判断填写指标值以及结果值"),
    MIN(40L, "MIN","取最小值，例：MIN(指标1，指标2...) 或者 MIN([1,2])"),
    LT(41L, "<","小于，搭配IFS使用：例：IFS(A < 1,1,0)"),
    GT(42L, ">","大于，搭配IFS使用：例：IFS(A > 1,1,0)"),
    ;

    private Long type;
    private String value;
    private String desc;

    OperationEnum(Long type, String value, String desc) {
        this.type = type;
        this.desc = desc;
        this.value = value;
    }
}
