package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.model.dto.QueryVerificationDto;
import com.dcas.common.domain.entity.CoBaseAssess;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 评估基础库表
 *
 * <AUTHOR>
 * @Date 2022/8/4 10:46
 * @ClassName CoAssessMapper
 */
public interface CoBaseAssessMapper extends BaseMapper<CoBaseAssess> {

    /**
     * 查询评估基础库列表
     *
     * @param dto request
     * @return * @return List<RetrieveVerificationVo>
     * @Date 2022/9/7 11:02
     */
    List<CoBaseAssess> queryBaseAssessList(QueryVerificationDto dto);

    /**
     * 查询评估基础库分析列表
     *
     * @param level       等级
     * @param modelName   模板名称
     * @param type        类型
     * @param stage       阶段
     * @param processArea 过程域
     * @param gpDimension GP维度
     * @return * @return List<CoBaseAssess>
     * @Date 2022/9/7 11:02
     */
    List<CoBaseAssess> queryBaseList(@Param("level") String level, @Param("modelName") String modelName, @Param("type") String type, @Param("stage") String stage, @Param("processArea") String processArea, @Param("gpDimension") String gpDimension);
}
