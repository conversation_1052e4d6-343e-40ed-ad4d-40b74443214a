package com.dcas.common.mapper;

import com.dcas.common.domain.entity.ScreenDatasource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.model.dto.ScreenDatasourceDTO;
import com.dcas.common.model.vo.ScreenDatasourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 大屏数源 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface ScreenDatasourceMapper extends BaseMapper<ScreenDatasource> {

    List<ScreenDatasourceDTO> queryBusSystemList(@Param("searchValue") String searchValue);

    void updateStatus(@Param("dto")ScreenDatasourceDTO dto);

    void updateDelFlag(@Param("systemId")Long systemId, @Param("sourceId")Integer sourceId);
}
