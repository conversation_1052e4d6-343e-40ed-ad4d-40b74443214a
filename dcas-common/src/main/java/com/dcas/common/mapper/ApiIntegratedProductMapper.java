package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.ApiIntegratedProduct;
import org.apache.ibatis.annotations.Param;

/**
 * 集成产品Mapper接口
 *
 * <AUTHOR>
 */
public interface ApiIntegratedProductMapper extends BaseMapper<ApiIntegratedProduct> {

    void updateStatusById(@Param("id") Long id, @Param("status") String status, @Param("logo") String logo);
}
