package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.ClauseItemRelevance;
import com.dcas.common.model.vo.ArticleItemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2023/3/7 14:39
 * @since 1.2.0
 */
public interface ClauseItemRelevanceMapper extends BaseMapper<ClauseItemRelevance> {

    List<ArticleItemVO> qryStandardItems(@Param("clauseId") String clauseId);
}
