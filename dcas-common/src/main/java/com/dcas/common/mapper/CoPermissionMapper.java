package com.dcas.common.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dcas.common.domain.entity.CoPermission;
import com.dcas.common.domain.entity.CoPermissionDetail;
import com.dcas.common.model.query.PermissionJobQuery;
import com.dcas.common.model.vo.PermissionJobVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/11/14 10:52
 * @since 1.0.0
 */
public interface CoPermissionMapper extends BaseMapper<CoPermission> {
    List<PermissionJobVO> pageQuery(@Param("query") PermissionJobQuery query);

    List<CoPermission> listByOperationId(@Param("operationId")String operationId);

    List<CoPermissionDetail> queryByOperationId(@Param("operationId") String operationId);
}
