package com.dcas.common.utils.excel;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Excel水印工具类测试
 *
 * <AUTHOR> Agent
 * @since 1.0.0
 */
class ExcelWatermarkUtilTest {

    @TempDir
    Path tempDir;

    /**
     * 测试数据类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TestData {
        @ExcelProperty("姓名")
        private String name;
        
        @ExcelProperty("年龄")
        private Integer age;
        
        @ExcelProperty("部门")
        private String department;
    }

    /**
     * 测试基本水印功能
     */
    @Test
    void testBasicWatermark() throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        workbook.createSheet("测试数据");

        // 添加简单水印
        ExcelWatermarkUtil.addSimpleWatermark(workbook, "机密文档");

        // 保存文件
        Path outputFile = tempDir.resolve("test_basic_watermark.xlsx");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            workbook.write(fos);
        }
        workbook.close();

        // 验证文件存在
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);
    }

    /**
     * 测试自定义水印配置
     */
    @Test
    void testCustomWatermarkConfig() throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        workbook.createSheet("测试数据");

        // 创建自定义水印配置
        WatermarkConfig config = WatermarkConfig.builder()
                .text("内部使用")
                .fontSize(24)
                .transparency(0.2f)
                .rotationAngle(-30.0)
                .build();

        // 添加水印
        ExcelWatermarkUtil.addWatermark(workbook, config);

        // 保存文件
        Path outputFile = tempDir.resolve("test_custom_watermark.xlsx");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            workbook.write(fos);
        }
        workbook.close();

        // 验证文件存在
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);
    }

    /**
     * 测试Hutool ExcelWriter集成
     */
    @Test
    void testHutoolExcelWriterIntegration() throws IOException {
        // 准备测试数据
        List<TestData> dataList = Arrays.asList(
                new TestData("张三", 25, "技术部"),
                new TestData("李四", 30, "销售部"),
                new TestData("王五", 28, "人事部")
        );

        // 使用Hutool ExcelWriter
        Path outputFile = tempDir.resolve("test_hutool_watermark.xlsx");
        try (ExcelWriter writer = ExcelUtil.getWriter()) {
            writer.setOnlyAlias(true);
            writer.write(dataList, true);

            // 添加水印
            ExcelWatermarkUtil.addSimpleWatermark(writer.getWorkbook(), "测试水印");

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
                writer.flush(fos, true);
            }
        }

        // 验证文件存在
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);
    }

    /**
     * 测试EasyExcel水印处理器
     */
    @Test
    void testEasyExcelWatermarkHandler() throws IOException {
        // 准备测试数据
        List<TestData> dataList = Arrays.asList(
                new TestData("张三", 25, "技术部"),
                new TestData("李四", 30, "销售部"),
                new TestData("王五", 28, "人事部")
        );

        // 使用EasyExcel导出带水印的文件
        Path outputFile = tempDir.resolve("test_easyexcel_watermark.xlsx");
        try (FileOutputStream fos = new FileOutputStream(outputFile.toFile())) {
            EasyExcel.write(fos, TestData.class)
                    .registerWriteHandler(EasyExcelWatermarkHandler.simple("EasyExcel水印"))
                    .sheet("员工数据")
                    .doWrite(dataList);
        }

        // 验证文件存在
        assertTrue(outputFile.toFile().exists());
        assertTrue(outputFile.toFile().length() > 0);
    }

    /**
     * 测试空参数处理
     */
    @Test
    void testNullParameterHandling() {
        // 测试空工作簿
        assertDoesNotThrow(() -> {
            ExcelWatermarkUtil.addSimpleWatermark(null, "测试");
        });

        // 测试空水印文本
        Workbook workbook = new XSSFWorkbook();
        workbook.createSheet("测试");
        
        assertDoesNotThrow(() -> {
            ExcelWatermarkUtil.addSimpleWatermark(workbook, null);
            ExcelWatermarkUtil.addSimpleWatermark(workbook, "");
            ExcelWatermarkUtil.addSimpleWatermark(workbook, "   ");
        });
        
        try {
            workbook.close();
        } catch (IOException e) {
            // 忽略关闭异常
        }
    }

    /**
     * 测试水印配置的默认值
     */
    @Test
    void testWatermarkConfigDefaults() {
        WatermarkConfig config = WatermarkConfig.defaultConfig("测试文本");
        
        assertEquals("测试文本", config.getText());
        assertEquals("宋体", config.getFontName());
        assertEquals(20, config.getFontSize());
        assertEquals(-45.0, config.getRotationAngle());
        assertEquals(0.3f, config.getTransparency());
        assertEquals(200, config.getHorizontalSpacing());
        assertEquals(150, config.getVerticalSpacing());
        assertFalse(config.isBold());
        assertFalse(config.isItalic());
    }

    /**
     * 测试多种水印配置
     */
    @Test
    void testMultipleWatermarkConfigs() throws IOException {
        // 机密文档水印
        WatermarkConfig confidential = WatermarkConfig.builder()
                .text("机密文档")
                .fontSize(24)
                .transparency(0.2f)
                .bold(true)
                .build();

        // 草稿版本水印
        WatermarkConfig draft = WatermarkConfig.builder()
                .text("草稿版本")
                .fontSize(18)
                .transparency(0.4f)
                .italic(true)
                .rotationAngle(-60.0)
                .build();

        // 测试不同配置
        for (WatermarkConfig config : Arrays.asList(confidential, draft)) {
            Workbook workbook = new XSSFWorkbook();
            workbook.createSheet("测试");
            
            assertDoesNotThrow(() -> {
                ExcelWatermarkUtil.addWatermark(workbook, config);
            });
            
            workbook.close();
        }
    }
}
