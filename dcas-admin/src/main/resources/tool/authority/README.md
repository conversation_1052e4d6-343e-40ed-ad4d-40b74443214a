# 数据权限检测离线工具 1.0

* 主要采用picocli、easyexcel，通过shell脚本方式生成数据权限检测导入模板

- 用法：
```shell
Usage: 数据权限检测离线工具 [-d=db name or instance name] -h=host -p=port
                  [-S=busSystem] -t=dbType -u=username -w=password
                  [-D=<String=String>]... [-v=properties]...

  -d, --dbName=db name or instance name
                            the check db name or instance name
  -D=<String=String>
  -h, --host=host           the check db ip address to connect to
  -p, --port=port           the check db ip port to connect to
  -S, --busSystem=busSystem the related busSystem
  -t, --dbType=dbType       0-"Oracle"
                            1-"SQL Server"
                            2-"MySQL"
                            3-"PostgreSQL"
                            12-"阿里云RDS - MySQL"
                            13-"阿里云RDS - PostgreSQL"
                            15-"阿里云RDS - MySQL5"
                            16-"MariaDB"
                            40-"达梦"
                            112-"TIDB"
                            121-"MYSQL5"
                            132-"OceanBase MySQL"
                            133-"OceanBase Oracle"
                            135-"OpenGauss"
                            144-"Kingbase86"
                            157-"PolarDB MySQL"
                            162-"PostgreSQL14"
                            168-"PolarDB PostgreSQL"
                            180-"MySQL_TD"
                            201-"PolarDB MySQL5"
  -u, --user=username       the check db ip login username
  -v, --properties=properties
                            advanced properties
  -w, --password=password   the check db ip login password
```

- 步骤：

1. 解压工具check.zip到目录 D:\check
2. 解压jre.zip到目录 D:\check,具体最终目录参考附录。
3. 切换到D:\check运行命令：
<br>```D:\check\jre\bin\java -jar D:\check\data-authority-check-1.0-SNAPSHOT.jar -h ************** -p 1521 -d ORCL -u hgh -w hgh -t 0```
<br>```D:\check\jre\bin\java  -DMCDATASOURCE_HOME=D:\check\mcdatasource -DMCDATASOURCE_VERSION='*******' -jar base-check-1.0-SNAPSHOT.jar -h ************** -p 1521 -d ORCL -u hgh -w hgh -t 0```
<br>高级参数：键值对方式:
<br>```D:\check\jre\bin\java -jar D:\check\data-authority-check-1.0-SNAPSHOT.jar -h ************* -p 3357 -u root -w 123456 -t 121 -v useSSL=false```
<br>高级参数：指定schema:
<br>```D:\check\jre\bin\java -jar D:\check\data-authority-check-1.0-SNAPSHOT.jar -h ************** -p 1521 -d ORCL -u hgh -w hgh -t 0 -S OA -v database-list=ANT,HGH```
4. 待运行结束，查看结果。check/*-authority.xlsx

- 附录：
```
D:\check
│─ jre
|─ mcdatasource
│  README.md
|  data-authority-check-1.0-SNAPSHOT.jar
|  conf.json
```