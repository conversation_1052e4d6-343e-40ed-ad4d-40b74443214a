create table if not exists pre_source_config
(
    id          serial
    constraint pre_source_config_pk
    primary key,
    operation_id varchar(100) not null,
    config_name varchar(100) not null,
    config_type integer,
    config_scan integer      not null,
    access_module integer,
    system_id   bigint,
    host        varchar(100),
    port        varchar(100),
    username    varchar(100),
    password    varchar(200),
    db_name     varchar(50),
    schemas     text,
    current_step integer default 1 not null,
    user_id     varchar(255) not null,
    config_id   integer
    );
comment on table pre_source_config is '前置数据源配置';
comment on column pre_source_config.id is '主键';
comment on column pre_source_config.operation_id is '作业id';
comment on column pre_source_config.config_name is '数据源名称';
comment on column pre_source_config.config_type is '数据源类型';
comment on column pre_source_config.config_scan is '扫描范围 1-数据库 2-主机';
comment on column pre_source_config.access_module is '评估模块 1-资产 2-权限 3-基础';
comment on column pre_source_config.system_id is '业务系统id';
comment on column pre_source_config.host is '主机地址';
comment on column pre_source_config.port is '端口';
comment on column pre_source_config.username is '用户名';
comment on column pre_source_config.password is '密码';
comment on column pre_source_config.db_name is '库名';
comment on column pre_source_config.schemas is 'schema名称';
comment on column pre_source_config.current_step is '当前步骤';
comment on column pre_source_config.user_id is '授权用户id集合';
comment on column pre_source_config.config_id is '关联source_config主键';
alter table pre_source_config
    owner to dcas;

create table if not exists pre_source_config_attach
(
    id        serial
    constraint pre_source_config_attach_pk
    primary key,
    config_id integer      not null,
    name      varchar(255) not null,
    value     varchar(255)
    );
comment on table pre_source_config_attach is '前置数据源配置附加表';
comment on column pre_source_config_attach.id is '主键';
comment on column pre_source_config_attach.config_id is '数据源配置id';
comment on column pre_source_config_attach.name is '配置名称';
comment on column pre_source_config_attach.value is '配置值';
alter table pre_source_config_attach
    owner to dcas;


CREATE TABLE if not exists spec_person_info_biz_type (
	"sequence" bigint NULL,
	"type" int2 NULL
);
COMMENT ON TABLE spec_person_info_biz_type IS '个人信息安全影响分类';
COMMENT ON COLUMN spec_person_info_biz_type."sequence" IS 'inner_sys_biz_data表主键ID';
COMMENT ON COLUMN spec_person_info_biz_type."type" IS '1、限制个人自主决定权；2、引发差别性待遇；3、个人名誉受损和遭受精神压力；4、人身财产受损';

create table if not exists discovery_classify_data
(
    id              bigserial
        constraint discovery_classify_data_pk
            primary key,
    operation_id    varchar(64)  not null,
    job_id          bigint       not null,
    inventory_id    varchar(64)  not null,
    column_name     varchar(255) not null,
    template_id     integer      not null,
    biz_id          bigint       not null,
    sensitive    bool  default false not null,
    data_id         integer      not null,
    asset_name      varchar(255) not null,
    type_id         integer      not null,
    belongs         varchar(255) not null,
    type_name       text         not null,
    sensitive_level integer      not null
    );
comment on table discovery_classify_data is '资产发现业务类型分类分级表';
comment on column discovery_classify_data.id is '主键';
comment on column discovery_classify_data.operation_id is '评估作业id';
comment on column discovery_classify_data.job_id is '资产发现作业id';
comment on column discovery_classify_data.inventory_id is '资产id';
comment on column discovery_classify_data.column_name is '列名';
comment on column discovery_classify_data.template_id is '资产模板id';
comment on column discovery_classify_data.biz_id is '业务类型id';
comment on column discovery_classify_data.sensitive is '是否敏感字段';
comment on column discovery_classify_data.data_id is '关联资产id';
comment on column discovery_classify_data.asset_name is '关联资产名称';
comment on column discovery_classify_data.type_id is '资产所属分类id';
comment on column discovery_classify_data.belongs is '所属分类';
comment on column discovery_classify_data.type_name is '所属分类名称';
comment on column discovery_classify_data.sensitive_level is '敏感等级';
alter table discovery_classify_data
    owner to dcas;


ALTER TABLE co_verification ALTER COLUMN standard_provision TYPE varchar(2000) USING standard_provision::varchar;

Do $$
BEGIN
BEGIN
alter table co_inventory add job_id bigint;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column job_id already exists in co_inventory.';
END;
END;
$$;
comment on column co_inventory.job_id is '资产发现作业id';

Do $$
BEGIN
BEGIN
alter table co_inventory add sensitive_column_num integer;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column sensitive_column_num already exists in co_inventory.';
END;
END;
$$;
comment on column co_inventory.sensitive_column_num is '敏感字段数量';

Do $$
BEGIN
BEGIN
alter table co_inventory add column_num integer;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column column_num already exists in co_inventory.';
END;
END;
$$;
comment on column co_inventory.column_num is '字段数量';

INSERT INTO library_sync_tables (table_name) VALUES ('discovery_template_content') ON conflict("table_name") do nothing;

Do $$
BEGIN
BEGIN
alter table co_asset_view add schema_name varchar(255);
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column schema_name already exists in co_asset_view.';
END;
END;
$$;
comment on column co_asset_view.schema_name is 'schema';

Do $$
BEGIN
BEGIN
alter table co_asset_user add job_id bigint;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column job_id already exists in co_asset_user.';
END;
END;
$$;
comment on column co_asset_user.job_id is '权限作业id';

Do $$
BEGIN
BEGIN
alter table co_db_user_authority add bus_system varchar(255);
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column bus_system already exists in co_db_user_authority.';
END;
END;
$$;
comment on column co_db_user_authority.bus_system is '业务系统名称';

CREATE TABLE  if not exists library_template_config (
	id bigserial NOT NULL,
	rel_id int4 NOT NULL,
	"enable" bool NOT NULL DEFAULT false,
	"type" int NOT NULL
);
COMMENT ON TABLE library_template_config IS '知识库模板启停状态配置';

-- Column comments
COMMENT ON COLUMN public.library_template_config.id IS '主键ID';
COMMENT ON COLUMN public.library_template_config.rel_id IS '关联模板ID';
COMMENT ON COLUMN public.library_template_config."enable" IS '启停状态';
COMMENT ON COLUMN public.library_template_config."type" IS '模板类型：1、合规模板；2、分析模型；3、调研模板；4、专项评估；5、资产识别模板；6、检查模板；7、风险模型；';

truncate table spec_person_info_biz_type;
insert into spec_person_info_biz_type (sequence, type) values (55865,1);
insert into spec_person_info_biz_type (sequence, type) values (55865,2);
insert into spec_person_info_biz_type (sequence, type) values (55838,1);
insert into spec_person_info_biz_type (sequence, type) values (55838,2);
insert into spec_person_info_biz_type (sequence, type) values (55838,3);
insert into spec_person_info_biz_type (sequence, type) values (55838,4);
insert into spec_person_info_biz_type (sequence, type) values (55777,4);
insert into spec_person_info_biz_type (sequence, type) values (55744,3);
insert into spec_person_info_biz_type (sequence, type) values (55744,4);
insert into spec_person_info_biz_type (sequence, type) values (55796,2);
insert into spec_person_info_biz_type (sequence, type) values (55796,3);
insert into spec_person_info_biz_type (sequence, type) values (55796,4);
insert into spec_person_info_biz_type (sequence, type) values (55832,4);
insert into spec_person_info_biz_type (sequence, type) values (55823,4);
insert into spec_person_info_biz_type (sequence, type) values (55831,3);
insert into spec_person_info_biz_type (sequence, type) values (55831,4);
insert into spec_person_info_biz_type (sequence, type) values (55828,4);
insert into spec_person_info_biz_type (sequence, type) values (55767,1);
insert into spec_person_info_biz_type (sequence, type) values (55767,2);
insert into spec_person_info_biz_type (sequence, type) values (55767,3);
insert into spec_person_info_biz_type (sequence, type) values (55767,4);
insert into spec_person_info_biz_type (sequence, type) values (55788,1);
insert into spec_person_info_biz_type (sequence, type) values (55788,2);
insert into spec_person_info_biz_type (sequence, type) values (55788,3);
insert into spec_person_info_biz_type (sequence, type) values (55788,4);
insert into spec_person_info_biz_type (sequence, type) values (55792,1);
insert into spec_person_info_biz_type (sequence, type) values (55792,2);
insert into spec_person_info_biz_type (sequence, type) values (55792,3);
insert into spec_person_info_biz_type (sequence, type) values (55792,4);
insert into spec_person_info_biz_type (sequence, type) values (55746,3);
insert into spec_person_info_biz_type (sequence, type) values (55746,4);
insert into spec_person_info_biz_type (sequence, type) values (55780,1);
insert into spec_person_info_biz_type (sequence, type) values (55780,2);
insert into spec_person_info_biz_type (sequence, type) values (55780,3);
insert into spec_person_info_biz_type (sequence, type) values (55780,4);
insert into spec_person_info_biz_type (sequence, type) values (55793,2);
insert into spec_person_info_biz_type (sequence, type) values (55793,3);
insert into spec_person_info_biz_type (sequence, type) values (55836,2);
insert into spec_person_info_biz_type (sequence, type) values (55836,3);
insert into spec_person_info_biz_type (sequence, type) values (55836,4);
insert into spec_person_info_biz_type (sequence, type) values (55813,1);
insert into spec_person_info_biz_type (sequence, type) values (55813,4);
insert into spec_person_info_biz_type (sequence, type) values (55845,1);
insert into spec_person_info_biz_type (sequence, type) values (55845,4);
insert into spec_person_info_biz_type (sequence, type) values (55751,2);
insert into spec_person_info_biz_type (sequence, type) values (55843,4);
insert into spec_person_info_biz_type (sequence, type) values (55753,2);
insert into spec_person_info_biz_type (sequence, type) values (55757,2);
insert into spec_person_info_biz_type (sequence, type) values (55825,1);
insert into spec_person_info_biz_type (sequence, type) values (55825,4);
insert into spec_person_info_biz_type (sequence, type) values (55781,1);
insert into spec_person_info_biz_type (sequence, type) values (55781,2);
insert into spec_person_info_biz_type (sequence, type) values (55781,3);
insert into spec_person_info_biz_type (sequence, type) values (55781,4);
insert into spec_person_info_biz_type (sequence, type) values (88968,2);
insert into spec_person_info_biz_type (sequence, type) values (88968,3);
insert into spec_person_info_biz_type (sequence, type) values (55743,3);
insert into spec_person_info_biz_type (sequence, type) values (55743,4);
insert into spec_person_info_biz_type (sequence, type) values (55739,2);
insert into spec_person_info_biz_type (sequence, type) values (55771,1);
insert into spec_person_info_biz_type (sequence, type) values (55771,4);
insert into spec_person_info_biz_type (sequence, type) values (55771,3);
insert into spec_person_info_biz_type (sequence, type) values (55764,2);
insert into spec_person_info_biz_type (sequence, type) values (55764,4);
insert into spec_person_info_biz_type (sequence, type) values (55854,2);
insert into spec_person_info_biz_type (sequence, type) values (55854,4);
insert into spec_person_info_biz_type (sequence, type) values (55854,3);
insert into spec_person_info_biz_type (sequence, type) values (55873,1);
insert into spec_person_info_biz_type (sequence, type) values (55873,2);
insert into spec_person_info_biz_type (sequence, type) values (55873,3);
insert into spec_person_info_biz_type (sequence, type) values (55741,1);
insert into spec_person_info_biz_type (sequence, type) values (55741,2);
insert into spec_person_info_biz_type (sequence, type) values (55800,2);
insert into spec_person_info_biz_type (sequence, type) values (55799,2);
insert into spec_person_info_biz_type (sequence, type) values (55801,2);
insert into spec_person_info_biz_type (sequence, type) values (55874,1);
insert into spec_person_info_biz_type (sequence, type) values (55874,2);
insert into spec_person_info_biz_type (sequence, type) values (55742,3);
insert into spec_person_info_biz_type (sequence, type) values (55790,2);
insert into spec_person_info_biz_type (sequence, type) values (55790,3);
insert into spec_person_info_biz_type (sequence, type) values (55827,4);
insert into spec_person_info_biz_type (sequence, type) values (55759,4);
insert into spec_person_info_biz_type (sequence, type) values (55798,2);
insert into spec_person_info_biz_type (sequence, type) values (55798,3);
insert into spec_person_info_biz_type (sequence, type) values (55803,2);
insert into spec_person_info_biz_type (sequence, type) values (55803,3);
insert into spec_person_info_biz_type (sequence, type) values (55749,2);
insert into spec_person_info_biz_type (sequence, type) values (55851,3);
insert into spec_person_info_biz_type (sequence, type) values (55851,4);
insert into spec_person_info_biz_type (sequence, type) values (55738,2);
insert into spec_person_info_biz_type (sequence, type) values (55876,2);
insert into spec_person_info_biz_type (sequence, type) values (55876,3);
insert into spec_person_info_biz_type (sequence, type) values (55876,4);
insert into spec_person_info_biz_type (sequence, type) values (55805,3);
insert into spec_person_info_biz_type (sequence, type) values (55805,2);
insert into spec_person_info_biz_type (sequence, type) values (55783,1);
insert into spec_person_info_biz_type (sequence, type) values (55783,2);
insert into spec_person_info_biz_type (sequence, type) values (55783,3);
insert into spec_person_info_biz_type (sequence, type) values (55754,2);
insert into spec_person_info_biz_type (sequence, type) values (55754,3);
insert into spec_person_info_biz_type (sequence, type) values (55784,1);
insert into spec_person_info_biz_type (sequence, type) values (55784,2);
insert into spec_person_info_biz_type (sequence, type) values (55784,3);
insert into spec_person_info_biz_type (sequence, type) values (55839,1);
insert into spec_person_info_biz_type (sequence, type) values (55839,2);
insert into spec_person_info_biz_type (sequence, type) values (55839,4);
insert into spec_person_info_biz_type (sequence, type) values (55766,1);
insert into spec_person_info_biz_type (sequence, type) values (55766,4);
insert into spec_person_info_biz_type (sequence, type) values (55844,4);
insert into spec_person_info_biz_type (sequence, type) values (55844,2);
insert into spec_person_info_biz_type (sequence, type) values (55844,3);
insert into spec_person_info_biz_type (sequence, type) values (55769,1);
insert into spec_person_info_biz_type (sequence, type) values (55769,4);
insert into spec_person_info_biz_type (sequence, type) values (55817,4);
insert into spec_person_info_biz_type (sequence, type) values (55829,3);
insert into spec_person_info_biz_type (sequence, type) values (55829,4);
insert into spec_person_info_biz_type (sequence, type) values (55806,4);
insert into spec_person_info_biz_type (sequence, type) values (55747,1);
insert into spec_person_info_biz_type (sequence, type) values (55747,2);
insert into spec_person_info_biz_type (sequence, type) values (55747,4);
insert into spec_person_info_biz_type (sequence, type) values (55860,2);
insert into spec_person_info_biz_type (sequence, type) values (55860,3);
insert into spec_person_info_biz_type (sequence, type) values (55750,1);
insert into spec_person_info_biz_type (sequence, type) values (55750,2);
insert into spec_person_info_biz_type (sequence, type) values (55750,3);
insert into spec_person_info_biz_type (sequence, type) values (55802,2);
insert into spec_person_info_biz_type (sequence, type) values (55802,3);
insert into spec_person_info_biz_type (sequence, type) values (55875,1);
insert into spec_person_info_biz_type (sequence, type) values (55875,2);
insert into spec_person_info_biz_type (sequence, type) values (55875,3);
insert into spec_person_info_biz_type (sequence, type) values (55782,2);
insert into spec_person_info_biz_type (sequence, type) values (55782,3);
insert into spec_person_info_biz_type (sequence, type) values (55782,4);
insert into spec_person_info_biz_type (sequence, type) values (55773,1);
insert into spec_person_info_biz_type (sequence, type) values (55773,4);
insert into spec_person_info_biz_type (sequence, type) values (55740,2);
insert into spec_person_info_biz_type (sequence, type) values (55760,2);
insert into spec_person_info_biz_type (sequence, type) values (55763,2);
insert into spec_person_info_biz_type (sequence, type) values (55763,3);
insert into spec_person_info_biz_type (sequence, type) values (55762,2);
insert into spec_person_info_biz_type (sequence, type) values (55762,3);
insert into spec_person_info_biz_type (sequence, type) values (55762,4);
insert into spec_person_info_biz_type (sequence, type) values (55859,4);
insert into spec_person_info_biz_type (sequence, type) values (55859,2);
insert into spec_person_info_biz_type (sequence, type) values (55791,1);
insert into spec_person_info_biz_type (sequence, type) values (55791,2);
insert into spec_person_info_biz_type (sequence, type) values (55791,3);
insert into spec_person_info_biz_type (sequence, type) values (55791,4);
insert into spec_person_info_biz_type (sequence, type) values (55837,1);
insert into spec_person_info_biz_type (sequence, type) values (55837,2);
insert into spec_person_info_biz_type (sequence, type) values (55837,3);
insert into spec_person_info_biz_type (sequence, type) values (55837,4);
insert into spec_person_info_biz_type (sequence, type) values (55737,4);
insert into spec_person_info_biz_type (sequence, type) values (55787,4);
insert into spec_person_info_biz_type (sequence, type) values (55787,2);
insert into spec_person_info_biz_type (sequence, type) values (55785,1);
insert into spec_person_info_biz_type (sequence, type) values (55785,2);
insert into spec_person_info_biz_type (sequence, type) values (55785,3);
insert into spec_person_info_biz_type (sequence, type) values (55785,4);
insert into spec_person_info_biz_type (sequence, type) values (55852,1);
insert into spec_person_info_biz_type (sequence, type) values (55852,4);
insert into spec_person_info_biz_type (sequence, type) values (55852,2);
insert into spec_person_info_biz_type (sequence, type) values (55745,1);
insert into spec_person_info_biz_type (sequence, type) values (55745,4);
insert into spec_person_info_biz_type (sequence, type) values (55745,2);
insert into spec_person_info_biz_type (sequence, type) values (55856,1);
insert into spec_person_info_biz_type (sequence, type) values (55856,4);
insert into spec_person_info_biz_type (sequence, type) values (55856,3);
insert into spec_person_info_biz_type (sequence, type) values (55779,1);
insert into spec_person_info_biz_type (sequence, type) values (55779,2);
insert into spec_person_info_biz_type (sequence, type) values (55779,3);
insert into spec_person_info_biz_type (sequence, type) values (55778,1);
insert into spec_person_info_biz_type (sequence, type) values (55778,2);
insert into spec_person_info_biz_type (sequence, type) values (55778,3);
insert into spec_person_info_biz_type (sequence, type) values (55821,1);
insert into spec_person_info_biz_type (sequence, type) values (55821,4);
insert into spec_person_info_biz_type (sequence, type) values (55808,4);
insert into spec_person_info_biz_type (sequence, type) values (55772,1);
insert into spec_person_info_biz_type (sequence, type) values (55772,4);
insert into spec_person_info_biz_type (sequence, type) values (55850,2);
insert into spec_person_info_biz_type (sequence, type) values (55850,3);
insert into spec_person_info_biz_type (sequence, type) values (55850,4);
insert into spec_person_info_biz_type (sequence, type) values (55826,1);
insert into spec_person_info_biz_type (sequence, type) values (55826,4);
insert into spec_person_info_biz_type (sequence, type) values (55758,4);
insert into spec_person_info_biz_type (sequence, type) values (55758,3);
insert into spec_person_info_biz_type (sequence, type) values (55774,1);
insert into spec_person_info_biz_type (sequence, type) values (55774,3);
insert into spec_person_info_biz_type (sequence, type) values (55774,4);
insert into spec_person_info_biz_type (sequence, type) values (55748,2);
insert into spec_person_info_biz_type (sequence, type) values (55871,1);
insert into spec_person_info_biz_type (sequence, type) values (55871,4);
insert into spec_person_info_biz_type (sequence, type) values (55842,1);
insert into spec_person_info_biz_type (sequence, type) values (55842,4);
insert into spec_person_info_biz_type (sequence, type) values (55842,3);
insert into spec_person_info_biz_type (sequence, type) values (55872,1);
insert into spec_person_info_biz_type (sequence, type) values (55872,4);
insert into spec_person_info_biz_type (sequence, type) values (55872,2);
insert into spec_person_info_biz_type (sequence, type) values (55857,2);
insert into spec_person_info_biz_type (sequence, type) values (55857,4);
insert into spec_person_info_biz_type (sequence, type) values (55820,4);
insert into spec_person_info_biz_type (sequence, type) values (55855,2);
insert into spec_person_info_biz_type (sequence, type) values (55797,1);
insert into spec_person_info_biz_type (sequence, type) values (55797,2);
insert into spec_person_info_biz_type (sequence, type) values (55797,3);
insert into spec_person_info_biz_type (sequence, type) values (55786,1);
insert into spec_person_info_biz_type (sequence, type) values (55786,4);
insert into spec_person_info_biz_type (sequence, type) values (55768,1);
insert into spec_person_info_biz_type (sequence, type) values (55768,4);
insert into spec_person_info_biz_type (sequence, type) values (55870,1);
insert into spec_person_info_biz_type (sequence, type) values (55870,2);
insert into spec_person_info_biz_type (sequence, type) values (55870,3);
insert into spec_person_info_biz_type (sequence, type) values (55870,4);
insert into spec_person_info_biz_type (sequence, type) values (55833,1);
insert into spec_person_info_biz_type (sequence, type) values (55833,4);
insert into spec_person_info_biz_type (sequence, type) values (55834,1);
insert into spec_person_info_biz_type (sequence, type) values (55834,4);
insert into spec_person_info_biz_type (sequence, type) values (55835,1);
insert into spec_person_info_biz_type (sequence, type) values (55835,4);
insert into spec_person_info_biz_type (sequence, type) values (55812,1);
insert into spec_person_info_biz_type (sequence, type) values (55812,4);
insert into spec_person_info_biz_type (sequence, type) values (55789,1);
insert into spec_person_info_biz_type (sequence, type) values (55789,2);
insert into spec_person_info_biz_type (sequence, type) values (55789,3);
insert into spec_person_info_biz_type (sequence, type) values (55789,4);
insert into spec_person_info_biz_type (sequence, type) values (55816,2);
insert into spec_person_info_biz_type (sequence, type) values (55816,3);
insert into spec_person_info_biz_type (sequence, type) values (55816,4);
insert into spec_person_info_biz_type (sequence, type) values (55814,4);
insert into spec_person_info_biz_type (sequence, type) values (55822,1);
insert into spec_person_info_biz_type (sequence, type) values (55822,4);
insert into spec_person_info_biz_type (sequence, type) values (55846,1);
insert into spec_person_info_biz_type (sequence, type) values (55846,4);
insert into spec_person_info_biz_type (sequence, type) values (55848,1);
insert into spec_person_info_biz_type (sequence, type) values (55848,4);
insert into spec_person_info_biz_type (sequence, type) values (55847,1);
insert into spec_person_info_biz_type (sequence, type) values (55847,4);
insert into spec_person_info_biz_type (sequence, type) values (55752,2);
insert into spec_person_info_biz_type (sequence, type) values (55752,3);
insert into spec_person_info_biz_type (sequence, type) values (55752,4);
insert into spec_person_info_biz_type (sequence, type) values (55794,2);
insert into spec_person_info_biz_type (sequence, type) values (55858,2);
insert into spec_person_info_biz_type (sequence, type) values (55858,3);
insert into spec_person_info_biz_type (sequence, type) values (55863,2);
insert into spec_person_info_biz_type (sequence, type) values (55863,3);
insert into spec_person_info_biz_type (sequence, type) values (55830,1);
insert into spec_person_info_biz_type (sequence, type) values (55830,3);
insert into spec_person_info_biz_type (sequence, type) values (55830,4);
insert into spec_person_info_biz_type (sequence, type) values (55849,1);
insert into spec_person_info_biz_type (sequence, type) values (55849,4);
insert into spec_person_info_biz_type (sequence, type) values (55853,4);
insert into spec_person_info_biz_type (sequence, type) values (96520,1);
insert into spec_person_info_biz_type (sequence, type) values (96520,4);
insert into spec_person_info_biz_type (sequence, type) values (55807,1);
insert into spec_person_info_biz_type (sequence, type) values (55807,4);
insert into spec_person_info_biz_type (sequence, type) values (55818,1);
insert into spec_person_info_biz_type (sequence, type) values (55818,4);
insert into spec_person_info_biz_type (sequence, type) values (55770,2);
insert into spec_person_info_biz_type (sequence, type) values (55770,3);
insert into spec_person_info_biz_type (sequence, type) values (55819,1);
insert into spec_person_info_biz_type (sequence, type) values (55819,4);
insert into spec_person_info_biz_type (sequence, type) values (55761,2);
insert into spec_person_info_biz_type (sequence, type) values (55761,3);
insert into spec_person_info_biz_type (sequence, type) values (55761,4);
insert into spec_person_info_biz_type (sequence, type) values (55765,3);
insert into spec_person_info_biz_type (sequence, type) values (55765,4);
insert into spec_person_info_biz_type (sequence, type) values (55755,3);
insert into spec_person_info_biz_type (sequence, type) values (55755,4);
insert into spec_person_info_biz_type (sequence, type) values (3157,1);
insert into spec_person_info_biz_type (sequence, type) values (3157,4);
insert into spec_person_info_biz_type (sequence, type) values (3187,3);
insert into spec_person_info_biz_type (sequence, type) values (3187,4);
insert into spec_person_info_biz_type (sequence, type) values (3240,2);
insert into spec_person_info_biz_type (sequence, type) values (3186,3);
insert into spec_person_info_biz_type (sequence, type) values (3186,4);
insert into spec_person_info_biz_type (sequence, type) values (3214,1);
insert into spec_person_info_biz_type (sequence, type) values (3214,2);
insert into spec_person_info_biz_type (sequence, type) values (3214,4);
insert into spec_person_info_biz_type (sequence, type) values (3210,1);
insert into spec_person_info_biz_type (sequence, type) values (3210,2);
insert into spec_person_info_biz_type (sequence, type) values (3210,3);
insert into spec_person_info_biz_type (sequence, type) values (3210,4);
insert into spec_person_info_biz_type (sequence, type) values (3156,4);
insert into spec_person_info_biz_type (sequence, type) values (3301,4);
insert into spec_person_info_biz_type (sequence, type) values (3300,4);
insert into spec_person_info_biz_type (sequence, type) values (3232,1);
insert into spec_person_info_biz_type (sequence, type) values (3232,2);
insert into spec_person_info_biz_type (sequence, type) values (3245,1);
insert into spec_person_info_biz_type (sequence, type) values (3158,1);
insert into spec_person_info_biz_type (sequence, type) values (3158,4);
insert into spec_person_info_biz_type (sequence, type) values (3221,1);
insert into spec_person_info_biz_type (sequence, type) values (3221,4);
insert into spec_person_info_biz_type (sequence, type) values (3227,4);
insert into spec_person_info_biz_type (sequence, type) values (3174,4);
insert into spec_person_info_biz_type (sequence, type) values (3174,2);
insert into spec_person_info_biz_type (sequence, type) values (3174,3);
insert into spec_person_info_biz_type (sequence, type) values (3223,1);
insert into spec_person_info_biz_type (sequence, type) values (3223,2);
insert into spec_person_info_biz_type (sequence, type) values (3223,4);
insert into spec_person_info_biz_type (sequence, type) values (3241,1);
insert into spec_person_info_biz_type (sequence, type) values (3241,2);
insert into spec_person_info_biz_type (sequence, type) values (3153,2);
insert into spec_person_info_biz_type (sequence, type) values (3178,2);
insert into spec_person_info_biz_type (sequence, type) values (3178,3);
insert into spec_person_info_biz_type (sequence, type) values (3178,4);
insert into spec_person_info_biz_type (sequence, type) values (3295,4);
insert into spec_person_info_biz_type (sequence, type) values (3295,3);
insert into spec_person_info_biz_type (sequence, type) values (3191,3);
insert into spec_person_info_biz_type (sequence, type) values (3191,4);
insert into spec_person_info_biz_type (sequence, type) values (3235,1);
insert into spec_person_info_biz_type (sequence, type) values (3235,2);
insert into spec_person_info_biz_type (sequence, type) values (3239,1);
insert into spec_person_info_biz_type (sequence, type) values (3239,2);
insert into spec_person_info_biz_type (sequence, type) values (3373,4);
insert into spec_person_info_biz_type (sequence, type) values (3373,3);
insert into spec_person_info_biz_type (sequence, type) values (3266,2);
insert into spec_person_info_biz_type (sequence, type) values (3266,3);
insert into spec_person_info_biz_type (sequence, type) values (3266,4);
insert into spec_person_info_biz_type (sequence, type) values (3169,2);
insert into spec_person_info_biz_type (sequence, type) values (3264,4);
insert into spec_person_info_biz_type (sequence, type) values (3264,3);


truncate table sys_menu;
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1, '工作台', 0, 1, '/home', '@/pages/home', '', '1', '0', 'C', '0', '0', '', 'home', 'admin', '2022-12-02 10:50:28.356074', '', null, '工作台');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (2, '客户视图', 0, 2, '/customerView', '@/pages/customerView', '', '1', '0', 'C', '0', '0', '', 'team', 'admin', '2022-12-02 10:50:28.357029', '', null, '客户视图');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (3, '评估准备', 0, 3, '/preparation', null, '', '1', '0', 'M', '0', '0', '', 'preparation', 'admin', '2022-12-02 10:50:28.357434', '', null, '评估准备');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (4, '评估管理', 0, 4, '/evaluation', null, '', '1', '0', 'M', '0', '0', '', 'exception', 'admin', '2022-12-02 10:50:28.357434', '', null, '评估管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (5, '规则管理', 0, 5, '/ruleManagement', null, '', '1', '0', 'M', '0', '0', '', 'rule', 'admin', '2022-12-02 10:50:28.357434', '', null, '规则管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (6, '审计管理', 0, 6, '/auditManagement', null, '', '1', '0', 'M', '0', '0', '', 'audit', 'admin', '2022-12-02 10:50:28.357434', '', null, '审计管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (7, '系统管理', 0, 7, '/setting', null, '', '1', '0', 'M', '0', '0', '', 'setting', 'admin', '2022-12-02 10:50:28.357434', '', null, '系统管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (100, '项目管理', 4, 1, 'assessment', '@/pages/assessment', '', '1', '0', 'C', '0', '0', 'project:add,project:edit,project:retrieve', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '项目管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (101, '项目详情信息', 4, 2, 'assessment/projectInfo/:id', '@/pages/assessment/projectInfo', '', '1', '0', 'C', '1', '0', '', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '项目详情信息');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (102, '评估作业', 4, 3, 'operationList', '@/pages/operationList', '', '1', '0', 'C', '0', '0', 'operation:add,operation:edit,operation:delete,operation:treeSelect,operation:query,operation:treeSelect,operation:copyjob,operation:use-template,operation:list-template,operation:get-risk-analysis-result', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '评估作业');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (103, '检查作业', 4, 4, 'securityCheckList', '@/pages/securityCheckList', '', '1', '0', 'C', '0', '0', 'system:user:list', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '检查作业');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (104, '资产发现', 3, 1, 'darkData', null, '', '1', '0', 'M', '0', '0', null, null, 'admin', '2022-12-02 10:50:28.358891', '', null, '资产发现');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (107, '登录日志', 6, 1, 'loginLog', '@/pages/loginLog', '', '1', '0', 'C', '0', '0', '', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '登录日志');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (108, '系统日志', 6, 2, 'systemLog', '@/pages/systemLog', '', '1', '0', 'C', '0', '0', '', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '系统日志');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (109, '操作日志', 6, 3, 'operationLog', '@/pages/operationLog', '', '1', '0', 'C', '0', '0', '', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '操作日志');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (110, '用户管理', 7, 1, 'userManagement', '@/pages/userManagement', '', '1', '0', 'C', '0', '0', 'system:user:list,system:user:export,system:user:query,system:user:add,system:user:edit,system:user:remove,system:user:resetPwd', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '用户管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (111, '角色管理', 7, 2, 'roleManagement', '@/pages/roleManagement', '', '1', '0', 'C', '0', '0', e'system:role:list,system:role:export,system:role:query,system:role:add,system:role:edit,system:role:remove,system:menu:list,system:menu:query,system:menu:add,system:menu:edit,system:menu:remove
', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '角色管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (112, '组织管理', 7, 3, 'departmentManagement', '@/pages/departmentManagement', '', '1', '0', 'C', '0', '0', 'system:dept:list,system:dept:list,system:dept:query,system:dept:add,system:dept:edit,system:dept:remove', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '组织管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (113, '运维管理', 7, 4, 'systemSettings', '@/pages/systemSettings', '', '1', '0', 'C', '0', '0', 'system:config:list,system:config:export,system:config:query,system:config:add,system:config:edit,system:config:remove', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '运维管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (114, '系统消息', 7, 5, 'logManagement', '@/pages/logManagement', '', '1', '0', 'C', '0', '0', '', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '系统消息');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (115, '授权管理', 7, 6, 'license', '@/pages/license', '', '1', '0', 'C', '0', '0', '', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '授权管理');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (116, '系统升级', 7, 7, 'systemUpgrade', '@/pages/systemUpgrade', '', '1', '0', 'C', '0', '0', 'system:config:list,system:config:export,system:config:query,system:config:add,system:config:edit,system:config:remove', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '系统升级');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1001, '暗数据', 104, 1, 'sourceList', '@/pages/darkData/dataClassify;@/pages/darkData/dataClassify', '', '0', '0', 'C', '0', '0', null, null, 'admin', '2022-12-02 10:50:28.358891', '', null, '暗数据');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, retrieve, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (105, '知识库模板', 5, 1, 'libraryTemplate', '@/pages/libraryTemplate', '', '1', '0', 'C', '0', '0', e'template:list,template:enable
', null, 'admin', '2022-12-02 10:50:28.358439', '', null, '知识库模板');





