create table if not exists co_system
(
    id        serial
        constraint co_system_pk
            primary key,
    name      varchar(255) not null,
    introduce varchar(1000) not null
);
comment on table co_system is '业务系统表';
comment on column co_system.id is '主键';
comment on column co_system.name is '业务系统名称';
comment on column co_system.introduce is '业务系统简介';
alter table co_system owner to dcas;

CREATE TABLE if not exists screen_config (
	id bigserial NOT NULL,
	"name" varchar(20) NOT NULL,
	bus_system_ids _int4 NULL,
	bus_system_names _varchar NULL,
	data_update_type int NULL,
	monitor_frequency int NULL,
	monitor_frequency_unit varchar DEFAULT 'h' NOT NULL,
	del_flag bool NULL,
	"template" text NULL,
	create_by varchar(64) NULL,
	create_time timestamp(6) NULL,
	update_by varchar(64) NULL,
	update_time timestamp(6) NULL,
	CONSTRAINT screen_config_pk PRIMARY KEY (id)
);
COMMENT ON TABLE screen_config IS '大屏配置表';

-- <PERSON>umn comments

COMMENT ON COLUMN screen_config.id IS '主键';
COMMENT ON COLUMN screen_config."name" IS '大屏名称';
COMMENT ON COLUMN screen_config.bus_system_ids IS '业务系统id数组';
COMMENT ON COLUMN screen_config.bus_system_names IS '业务系统名称数组';
COMMENT ON COLUMN screen_config.data_update_type IS '数据更新方式：1-连接数源动态监测；2-根据已结项作业更新';
COMMENT ON COLUMN screen_config."template" IS '大屏模板';
COMMENT ON COLUMN screen_config.monitor_frequency IS '检测频率';
COMMENT ON COLUMN screen_config.monitor_frequency_unit IS '检测频率单位，默认：小时（h）';
COMMENT ON COLUMN screen_config.del_flag IS '删除标志';
COMMENT ON COLUMN screen_config.create_by IS '创建人';
COMMENT ON COLUMN screen_config.create_time IS '创建时间';
COMMENT ON COLUMN screen_config.update_by IS '修改人';
COMMENT ON COLUMN screen_config.update_time IS '修改时间';


CREATE TABLE if not exists screen_datasource (
	id bigserial NOT NULL,
	system_id int4 NULL,
	source_id int4 NULL,
	status int DEFAULT 1 NOT NULL,
	del_flag bool NULL,
	create_by varchar(64) NULL,
	create_time timestamp(6) NULL,
	update_by varchar(64) NULL,
	update_time timestamp(6) NULL,
	CONSTRAINT screen_datasource_pk PRIMARY KEY (id)
);
COMMENT ON TABLE screen_datasource IS '大屏数源';

-- Column comments

COMMENT ON COLUMN screen_datasource.id IS '主键ID';
COMMENT ON COLUMN screen_datasource.system_id IS '业务系统ID';
COMMENT ON COLUMN screen_datasource.source_id IS '数源ID';
COMMENT ON COLUMN screen_datasource.status IS '状态：0-失败 1-成功';
COMMENT ON COLUMN screen_config.del_flag IS '删除标志';
COMMENT ON COLUMN screen_config.create_by IS '创建人';
COMMENT ON COLUMN screen_config.create_time IS '创建时间';
COMMENT ON COLUMN screen_config.update_by IS '修改人';
COMMENT ON COLUMN screen_config.update_time IS '修改时间';


create table if not exists screen_hot_resources
(
    id           bigserial
        constraint screen_hot_resources_pk
            primary key,
    type         integer       not null,
    term_id      integer       not null,
    name         varchar(1000) not null,
    source_id    integer       not null,
    system_id    integer       not null,
    access_count bigint        not null
);
comment on table screen_hot_resources is '热点资源表';
comment on column screen_hot_resources.id is '主键';
comment on column screen_hot_resources.type is '热点资源类型 1-资产访问频率 2-用户使用频率';
comment on column screen_hot_resources.term_id is '检查点id';
comment on column screen_hot_resources.name is '热点资源名称';
comment on column screen_hot_resources.access_count is '访问次数';
comment on column screen_hot_resources.source_id is '数据源id';
comment on column screen_hot_resources.system_id is '业务系统id';
alter table screen_hot_resources
    owner to dcas;


CREATE TABLE if not exists screen_summary_resources (
	id bigserial NOT NULL,
	"type" int NOT NULL,
	"name" varchar(1000) NOT NULL,
	result varchar(4000) NOT NULL,
	create_time  timestamp(6) NULL,
	source_id int4 NULL,
	system_id int4 NOT NULL,
	CONSTRAINT screen_summary_resources_pk PRIMARY KEY (id)
);
COMMENT ON COLUMN screen_summary_resources."type" IS '统计类型：1-网络资产，2-风险API，3-风险模型';
COMMENT ON COLUMN screen_summary_resources.id IS '主键ID';
COMMENT ON COLUMN screen_summary_resources."name" IS '名称';
COMMENT ON COLUMN screen_summary_resources.result IS '结果-数量';
COMMENT ON COLUMN screen_summary_resources.source_id IS '数源ID';
COMMENT ON COLUMN screen_summary_resources.system_id IS '业务系统ID';
COMMENT ON COLUMN screen_summary_resources.create_time IS '创建时间';
COMMENT ON TABLE screen_summary_resources IS '大屏统计表';
alter table screen_summary_resources
    owner to dcas;