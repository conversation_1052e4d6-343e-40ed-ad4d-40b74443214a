select add_column_to_tables('law_item', 'has_exception', 'boolean');

create table if not exists questionnaire_item_degree
(
    id           serial
        constraint questionnaire_item_degree_pk
            primary key,
    operation_id varchar(64)         not null,
    template_id  integer             not null,
    item_id      varchar(64)         not null,
    degree       integer default 100 not null
);
comment on table questionnaire_item_degree is '核查项符合程度表（汽车专用）';
comment on column questionnaire_item_degree.id is '主键';
comment on column questionnaire_item_degree.operation_id is '作业id';
comment on column questionnaire_item_degree.template_id is '调研模板id';
comment on column questionnaire_item_degree.item_id is '核查项id';
comment on column questionnaire_item_degree.degree is '符合程度';
alter table questionnaire_item_degree
    owner to dcas;

INSERT INTO library_sync_tables (table_name) VALUES ('file_analysis_logic') ON conflict("table_name") do nothing;
