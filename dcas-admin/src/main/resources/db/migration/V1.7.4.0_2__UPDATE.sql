alter table public.co_verification alter column description type text using description::text;

select add_column_to_tables('model_config', 'impact_library_id', 'int8');


CREATE TABLE IF NOT EXISTS co_impact_analysis (
	impact_id varchar NOT NULL,
	operation_id varchar NULL,
	label_id int8 NULL,
	bus_system varchar(255) NULL,
	impact_type varchar(2000) NULL,
	impact_level varchar(255) NULL,
	CONSTRAINT co_impact_analysis_pkey PRIMARY KEY (impact_id)
);
CREATE INDEX IF NOT EXISTS co_impact_analysis_idx ON co_impact_analysis ("operation_id","bus_system");
COMMENT ON TABLE co_impact_analysis IS '影响分析表';
COMMENT ON COLUMN co_impact_analysis.impact_id IS '影响类别ID';
COMMENT ON COLUMN co_impact_analysis.operation_id IS '作业ID';
COMMENT ON COLUMN co_impact_analysis.label_id IS '标签ID';
COMMENT ON COLUMN co_impact_analysis.bus_system IS '业务系统';
COMMENT ON COLUMN co_impact_analysis.impact_type IS '影响类别';
COMMENT ON COLUMN co_impact_analysis.impact_level IS '影响级别';
alter table co_impact_analysis
    owner to dcas;


Do $$
BEGIN
BEGIN
alter table co_verification
    add incompatible text;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column incompatible already exists in co_verification.';
END;
END;
$$;
comment on column co_verification.incompatible is '现状分析不符合描述';

Do $$
BEGIN
BEGIN
alter table co_legal
    add incompatible text;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column incompatible already exists in co_legal.';
END;
END;
$$;
comment on column co_legal.incompatible is '现状分析不符合描述';

INSERT INTO library_sync_tables (table_name) VALUES ('impact_library') ON conflict("table_name") do nothing;
INSERT INTO library_sync_tables (table_name) VALUES ('impact_tree') ON conflict("table_name") do nothing;

select add_column_to_tables('risk_strategy', 'describe', 'varchar(255)');
