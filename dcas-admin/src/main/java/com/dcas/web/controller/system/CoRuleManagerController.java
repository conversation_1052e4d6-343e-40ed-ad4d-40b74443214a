package com.dcas.web.controller.system;

import com.dcas.common.annotation.Log;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.exception.params.FailParamsException;
import com.github.pagehelper.PageInfo;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.model.dto.AddModelDto;
import com.dcas.common.model.dto.PrimaryKeyListDTO;
import com.dcas.common.model.dto.QueryModelDTO;
import com.dcas.common.model.dto.UpdateModelDTO;
import com.dcas.common.domain.entity.CoModel;
import com.dcas.system.service.impl.CoRuleManagerServiceImpl;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 规则管理控制层
 *
 * <AUTHOR>
 * @Date 2022/6/15 17:52
 * @ClassName RuleManagerController
 */
@Controller
@RequestMapping(value = "/api/rule")
@Api(tags = "规则管理")
public class CoRuleManagerController {

    @Autowired
    private CoRuleManagerServiceImpl ruleManagerService;

    /**
     * 新增合规模板
     *
     * @param dto request
     * @return * @return ResponseApi
     * @Date 2022/6/15 17:50
     */
    @ResponseBody
    @PostMapping(value = "/add")
    @Log(title = "新增合规模板", businessType = BusinessType.INSERT)
    public ResponseApi add(@RequestBody RequestModel<AddModelDto> dto) {
        try {
            int row = ruleManagerService.add(dto);
            return row > 0 ? ResponseApi.ok() : ResponseApi.fail();
        } catch (FailParamsException e) {  //捕捉入参校验异常

            return ResponseApi.fail(e.getMessage());

        } catch (Exception e) {  //其他异常

            return ResponseApi.fail();
        }
    }

    /**
     * 删除合规模板
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 17:03
     */
    @ResponseBody
    @PostMapping(value = "/remove")
    @Log(title = "删除合规模板", businessType = BusinessType.DELETE)
    public ResponseApi remove(@RequestBody RequestModel<PrimaryKeyListDTO> dto) {
        try {
            int row = ruleManagerService.delete(dto);
            return row > 0 ? ResponseApi.ok() : ResponseApi.fail();
        } catch (RuntimeException e) { //运行时异常

            return ResponseApi.fail(e.getMessage());

        } catch (Exception e) {  //其他异常
            e.printStackTrace();
            return ResponseApi.fail();
        }

    }

    /**
     * 更新项目
     *
     * @param dto request
     * @return * @return RestResponse
     * <AUTHOR>
     * @Date 2022/5/16 17:04
     */
    @ResponseBody
    @PostMapping(value = "/edit")
    @Log(title = "更新项目", businessType = BusinessType.UPDATE)
    public ResponseApi<String> edit(@RequestBody RequestModel<UpdateModelDTO> dto) {
        try {
            int row = ruleManagerService.update(dto);
            return row > 0 ? ResponseApi.ok() : ResponseApi.fail();
        } catch (RuntimeException e) { //运行时异常

            return ResponseApi.fail(e.getMessage());

        } catch (Exception e) {  //其他异常

            return ResponseApi.fail();
        }
    }

}
