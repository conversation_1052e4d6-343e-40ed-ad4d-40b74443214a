package com.dcas.web.controller.screen;

import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.enums.CommonResultCode;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.CustomerIdDto;
import com.dcas.common.model.dto.ScreenConfigDTO;
import com.dcas.common.model.vo.QueryStatusView;
import com.dcas.common.model.vo.ScreenConfigVO;
import com.dcas.system.service.IScreenConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @className ScreenConfigController
 * @description 大屏配置controller
 * @date 2025/01/08 09:36
 */
@Slf4j
@RestController
@RequestMapping(value = "/api/screen")
@Api(tags = "大屏配置")
@RequiredArgsConstructor
public class ScreenConfigController {

    private final IScreenConfigService iScreenConfigService;

    /**
     * 查询客户业务情况
     */
    @ApiOperation(value = "查询大屏模板列表")
    @GetMapping(value = "/list")
    public ResponseApi<List<ScreenConfigVO>> listAll() {
        try {
            List<ScreenConfigVO> list =  iScreenConfigService.listAll();
            return ResponseApi.ok(list);
        } catch (Exception e) {  //其他异常
            log.info("查询大屏模板列表失败!", e);
            return ResponseApi.fail();
        }
    }

    @ApiOperation(value = "保存大屏配置")
    @PostMapping(value = "/save")
    @Log(title = "大屏配置-保存大屏配置", businessType = BusinessType.INSERT, module = "大屏配置")
    public ResponseApi<Void> save(@RequestBody ScreenConfigDTO screenConfigDTO) {
        try {
            iScreenConfigService.saveScreenConfig(screenConfigDTO);
            return ResponseApi.ok();
        } catch (ServiceException e) {  //其他异常
            log.info("保存大屏配置失败!", e);
            if (e.getCode() != null && e.getCode() == CommonResultCode.MC_SCREEN_VALID_ERROR.getCode()){
                return ResponseApi.fail(CommonResultCode.MC_SCREEN_VALID_ERROR.getCode(), e.getMessage());
            }
            return ResponseApi.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "更新大屏配置")
    @PostMapping(value = "/edit")
    @Log(title = "大屏配置-更新大屏配置", businessType = BusinessType.UPDATE, module = "大屏配置")
    public ResponseApi<Void> edit(@RequestBody ScreenConfigDTO screenConfigDTO) {
        try {
            iScreenConfigService.updateScreenConfig(screenConfigDTO);
            return ResponseApi.ok();
        } catch (ServiceException e) {  //其他异常
            log.info("更新大屏配置失败!", e);
            if (e.getCode() == CommonResultCode.MC_SCREEN_VALID_ERROR.getCode()){
                return ResponseApi.fail(CommonResultCode.MC_SCREEN_VALID_ERROR.getCode(), e.getMessage());
            }
            return ResponseApi.fail(e.getMessage());
        }
    }
}
