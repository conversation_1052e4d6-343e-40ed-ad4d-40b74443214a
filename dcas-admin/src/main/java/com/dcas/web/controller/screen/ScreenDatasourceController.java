package com.dcas.web.controller.screen;


import com.dcas.common.annotation.Log;
import com.dcas.common.core.domain.ResponseApi;
import com.dcas.common.enums.BusinessType;
import com.dcas.common.model.dto.ScreenConfigDTO;
import com.dcas.common.model.dto.ScreenDatasourceDTO;
import com.dcas.common.model.vo.ScreenConfigVO;
import com.dcas.common.model.vo.ScreenDatasourceVO;
import com.dcas.system.domain.resp.TestConnectionResponse;
import com.dcas.system.service.IScreenDatasourceService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 大屏数源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/screen-datasource")
@Api(tags = "大屏数源配置")
@RequiredArgsConstructor
public class ScreenDatasourceController {

    private final IScreenDatasourceService iScreenDatasourceService;

    /**
     * 查询客户业务情况
     */
    @ApiOperation(value = "查询大屏数源列表")
    @GetMapping(value = "/page")
    public ResponseApi<PageInfo<ScreenDatasourceVO>> page(
        @RequestParam("pageNum") Integer pageNum ,
        @RequestParam("pageSize")Integer pageSize,
        @RequestParam(value = "searchValue", required = false)String searchValue) {
        try {
            PageInfo<ScreenDatasourceVO> list =  iScreenDatasourceService.page(pageNum,pageSize,searchValue);
            return ResponseApi.ok(list);
        } catch (Exception e) {  //其他异常
            log.info("查询大屏模板列表失败!", e);
            return ResponseApi.fail();
        }
    }

    @ApiOperation(value = "保存大屏数源配置")
    @PostMapping(value = "/save")
    @Log(title = "大屏数源配置-保存大屏数源配置", businessType = BusinessType.INSERT, module = "大屏数源配置")
    public ResponseApi<Void> save(@RequestBody ScreenDatasourceDTO dto) {
        try {
            iScreenDatasourceService.saveScreenDatasource(dto);
            return ResponseApi.ok();
        } catch (Exception e) {  //其他异常
            log.info("保存大屏数源配置失败!", e);
            return ResponseApi.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "更新大屏数源配置")
    @PostMapping(value = "/edit")
    @Log(title = "大屏数源配置-更新大屏数源配置", businessType = BusinessType.UPDATE, module = "大屏数源配置")
    public ResponseApi<Void> edit(@RequestBody ScreenDatasourceDTO dto) {
        try {
            iScreenDatasourceService.updateScreenDatasource(dto);
            return ResponseApi.ok();
        } catch (Exception e) {  //其他异常
            log.info("更新大屏数源配置失败!", e);
            return ResponseApi.fail();
        }
    }

    @ApiOperation(value = "删除业务系统")
    @DeleteMapping(value = "/remove/{systemIds}")
    @Log(title = "大屏数源配置-删除业务系统", businessType = BusinessType.DELETE, module = "大屏数源配置")
    public ResponseApi<Void> remove(@ApiParam("业务系统id数组")@PathVariable Integer[] systemIds) {
        try {
            iScreenDatasourceService.removeBySystemIds(systemIds);
            return ResponseApi.ok();
        } catch (Exception e) {  //其他异常
            log.info("更新大屏数源配置失败!", e);
            return ResponseApi.fail();
        }
    }


    @ApiOperation(value = "单个数源测试连接")
    @GetMapping(value = "/test/{id}")
    public ResponseApi<TestConnectionResponse> testConnect(@ApiParam("数源ID") @PathVariable Integer id) {
        try {
            return ResponseApi.ok(iScreenDatasourceService.testConnect(id));
        } catch (Exception e) {  //其他异常
            return ResponseApi.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "根据业务系统测试连接")
    @GetMapping(value = "/test/all/{systemId}")
    public ResponseApi<TestConnectionResponse> testConnectBySystemId(@ApiParam("业务系统ID") @PathVariable Integer systemId) {
        try {
            return ResponseApi.ok(iScreenDatasourceService.testConnectBySystemId(systemId));
        } catch (Exception e) {  //其他异常
            return ResponseApi.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "获取可用的业务系统列表")
    @DeleteMapping(value = "/list/busSystem")
    public ResponseApi<List<ScreenDatasourceVO>> getBusSystemList() {
        try {
            return ResponseApi.ok(iScreenDatasourceService.getBusSystemList());
        } catch (Exception e) {  //其他异常
            return ResponseApi.fail(e.getMessage());
        }
    }

}
