package com.dcas.web.controller.system;

import com.dcas.common.core.domain.R;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.dto.SystemAddDTO;
import com.dcas.common.model.dto.SystemEditDTO;
import com.dcas.common.model.dto.SystemQueryDTO;
import com.dcas.common.model.dto.SystemSourceAdd;
import com.dcas.common.model.req.IdsReq;
import com.dcas.common.model.vo.SystemDetailVO;
import com.dcas.common.model.vo.SystemSourceVO;
import com.dcas.common.utils.PageResult;
import com.dcas.system.service.CoSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/1/8 17:07
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/system")
@RequiredArgsConstructor
@Api(tags = "业务系统")
public class CoSystemController {

    private final CoSystemService coSystemService;

    @GetMapping("/list")
    @ApiOperation("分页查询业务系统列表")
    public R<PageResult<SystemDetailVO>> pageQuery(SystemQueryDTO dto) {
        return R.success(coSystemService.pageQuery(dto));
    }

    @PostMapping
    @ApiOperation("新增业务系统")
    public R<Boolean> add(@Validated @RequestBody SystemAddDTO dto) {
        coSystemService.add(dto);
        return R.success();
    }

    @PutMapping
    @ApiOperation("修改业务系统")
    public R<Boolean> update(@Validated @RequestBody SystemEditDTO dto) {
        coSystemService.update(dto);
        return R.success();
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除业务系统")
    public R<Boolean> delete(@Validated @RequestBody IdsReq ids) {
        try {
            coSystemService.delete(ids);
            return R.success();
        } catch (ServiceException e){
            return R.fail(e.getMessage());
        }
    }

    @GetMapping("/source")
    @ApiOperation("查询可供绑定的数据源列表")
    public R<List<SystemSourceVO>> getSource(Integer id) {
        return R.success(coSystemService.getSource(id));
    }

    @PostMapping("/addSource")
    @ApiOperation("业务系统绑定数据源")
    public R<Boolean> addSource(@Validated @RequestBody SystemSourceAdd req) {
        coSystemService.addSource(req);
        return R.success();
    }

    @PostMapping("/deleteSource")
    @ApiOperation("业务系统解绑数据源")
    public R<Boolean> deleteSource(@Validated @RequestBody SystemSourceAdd req) {
        coSystemService.deleteSource(req);
        return R.success();
    }
}
