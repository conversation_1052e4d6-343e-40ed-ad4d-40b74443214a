package com.dcas.web.core.runner;

import com.mchz.starter.sso.support.SsoClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/1/5 11:23
 * @since 1.4.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(
        value = {"mc.sso.enabled"},
        havingValue = "true",
        matchIfMissing = true)
public class SsoPermissionActiveRunner implements ApplicationRunner {

    private final SsoClient ssoClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            ssoClient.upgradeApplication();
        } catch (Exception e) {
            log.info("[MC - SSO] The application version remains unchanged and does not require updates.");
        }
    }
}
