package com.dcas.web.core.config;

import com.dcas.common.enums.AuthType;
import com.mchz.starter.sso.support.IPermission;
import com.mchz.starter.sso.support.UserCenterActivated;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/1/4 14:05
 * @since 1.4.0
 */
@Configuration
@ConditionalOnProperty(
        value = {"mc.sso.enabled"},
        havingValue = "true",
        matchIfMissing = true)
public class DefinedUserCenterActivated extends UserCenterActivated {

    @Override
    public Collection<? extends IPermission> getPermissions() {
        List<IPermission> res = new ArrayList<>();
        for (AuthType type : AuthType.values()) {
            if (type.isRegister()) {
                res.add(new Permission(type.getCode(), type.getName(), type.getParentCode()));
            }
        }
        return res;
    }

    @Getter
    @AllArgsConstructor
    private static class Permission implements IPermission {
        private final String code;
        private final String name;
        private final String parentCode;
    }
}
