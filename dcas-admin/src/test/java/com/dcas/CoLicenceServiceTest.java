package com.dcas;

import com.dcas.common.core.domain.RequestModel;
import com.dcas.common.domain.entity.CoLicense;
import com.dcas.system.service.CoLicenseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * license接口单元测试
 *
 * <AUTHOR>
 * @date 2023/11/20 14:53
 **/
@SpringBootTest
public class CoLicenceServiceTest {

    @Autowired
    private CoLicenseService coLicenseService;

    @Test
    public void testQueryLicense(){
        RequestModel<CoLicense> requestModel = new RequestModel();
        CoLicense coLicense = new CoLicense();
        coLicense.setDeviceId("699053265f4e4d5342040032463a5c302f52aecdec48214a60b2fb739e114231");
        requestModel.setPrivator(coLicense);
        System.out.println(coLicenseService.queryLicenseResult(requestModel));
    }

    @Test
    public void testSendHealth(){
        coLicenseService.doSendTerminalHearth("699053265f4e4d5342040032463a5c302f52aecdec48214a60b2fb739e114231");
    }
}
