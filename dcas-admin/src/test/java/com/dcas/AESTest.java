package com.dcas;

import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.Digester;
import cn.hutool.crypto.digest.MD5;
import com.dcas.common.utils.SecurityUtils;
import com.dcas.common.utils.sign.Md5Utils;
import com.dcas.discovery.util.SampleDataCryptoComponent;
import org.apache.commons.codec.digest.Md5Crypt;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.Base64;

/**
 * <AUTHOR>
 * @className AESTest
 * @description aes加密测试
 * @date 2024/08/15 15:03
 */
@SpringBootTest
public class AESTest {

    @Autowired
    private SampleDataCryptoComponent sampleDataCryptoComponent;

    @Test
    public void test(){
        String result = sampleDataCryptoComponent.encrypt("你好呀");
//        System.out.println(result);
//        Assertions.assertEquals("你好呀", sampleDataCryptoComponent.decrypt(result));
    }

    @Test
    public void testPassWd(){
        // 修改logo密文生成，用token进行aes加密后md5Hex16处理subString(8,24)
        String result = SecurityUtils.encryptAes("eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImM0OGEwZTcxLTE5NGItNDRlOC05OGM1LTdmZTk1ZTUyOWY0MyJ9.3p05-4HK1a6Ql7SEQ_1jb_xleXQkIaUNY4dzjOzaeLlAqaaPiftJwSAyhgidtGiVzXqTDmhHnfXvF-Fug5NKOA");
        System.out.println(result);
        System.out.println(SecurityUtils.decryptAes(result));
        String res = DigestUtil.md5Hex16(result);
        System.out.println(res);
    }


}
