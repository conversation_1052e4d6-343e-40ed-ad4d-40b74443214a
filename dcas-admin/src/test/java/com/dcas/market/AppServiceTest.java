package com.dcas.market;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dcas.StartApplication;
import com.dcas.market.app.model.App;
import com.dcas.market.app.model.InstallParam;
import com.dcas.market.app.model.vo.AppDetailVO;
import com.dcas.market.app.model.vo.AppVO;
import com.dcas.market.app.service.IAppService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.net.URL;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 * @className AppServiceTest
 * @description App接口单元测试类
 * @date 2024/05/29 09:42
 */
@SpringBootTest(classes = {StartApplication.class})
public class AppServiceTest {

    @Autowired
    private IAppService iAppService;

    @Test
    public void testAppList() throws IOException {
        PageInfo<AppVO> appList = iAppService.getAppList(1, 60, "");
    }

    @Test
    public void testInstallApp() throws Exception {
        String json =
            "{\n" + "    \"appDetailId\": 149,\n" + "    \"params\": {\n" + "        \"APP_PORT_HTTP\": 5244\n" + "    },\n" + "    \"name\": \"alist\",\n" + "    \"advanced\": true,\n" + "    \"cpuQuota\": 0,\n" + "    \"memoryLimit\": 0,\n" + "    \"memoryUnit\": \"M\",\n" + "    \"containerName\": \"\",\n" + "    \"allowPort\": true,\n" + "    \"editCompose\": true,\n" + "    \"dockerCompose\": \"services:\\n  alist:\\n    container_name: ${CONTAINER_NAME}\\n    restart: always\\n    networks:\\n      - 1panel-network\\n    ports:\\n      - \\\"${APP_PORT_HTTP}:5244\\\"\\n    volumes:\\n      - ./data/data:/opt/alist/data\\n      - ./data/mnt:/mnt/data\\n    environment:\\n      - PUID=0\\n      - PGID=0\\n      - UMASK=022\\n    image: xhofe/alist:v3.34.0\\n    labels:  \\n      createdBy: \\\"Apps\\\"\\nnetworks:  \\n  dcas-network:  \\n    external: true\\n\\n\",\n" + "    \"version\": \"3.34.0\",\n" + "    \"appID\": \"74\",\n" + "    \"pullImage\": true\n" + "}";
        InstallParam installParam = JSON.parseObject(json, InstallParam.class);
    }

    @Test
    public void test() throws IOException {
        String icon = "https://apps-assets.fit2cloud.com/stable/1panel/mcsmanager-web/logo.png";
        URL url = new URL(icon);
        BASE64Encoder base64Encoder = new BASE64Encoder();
//        System.out.println(iconStr);
        System.out.println(Base64.getEncoder().encodeToString(IOUtils.toByteArray(url)));
    }

    @Test
    public void testDownloadAppComposeFile() throws Exception {
        String http = "https://apps-assets.fit2cloud.com/stable/1panel/mblog/1.1.0/mblog-1.1.0.tar.gz";
        List<App> apps = iAppService.getAppList("classpath:market.json");
        iAppService.saveOrUpdate(apps);
    }

    @Test
    public void testGetAppDetails() throws IOException {
        AppDetailVO appDetailVO = iAppService.getAppDetail(1, "2.3.2");
        System.out.println(JSONUtil.toJsonStr(appDetailVO));

    }

}
