package com.dcas.discovery.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dcas.common.domain.entity.ResultMetaTable;
import com.dcas.common.mapper.ResultMetaTableMapper;
import com.dcas.discovery.service.ResultMetaTableService;
import org.springframework.stereotype.Service;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/3/27 9:30
 * @since 1.7.2
 */
@Service
public class ResultMetaTableServiceImpl extends ServiceImpl<ResultMetaTableMapper, ResultMetaTable> implements ResultMetaTableService {

}
