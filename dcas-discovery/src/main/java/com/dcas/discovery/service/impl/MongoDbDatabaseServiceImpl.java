package com.dcas.discovery.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.dcas.common.api.MetaConst;
import com.dcas.common.core.param.DataSourceParam;
import com.dcas.common.enums.Authentication;
import com.dcas.common.enums.DataTypeGroup;
import com.dcas.common.exception.ServiceException;
import com.dcas.common.model.other.*;
import com.dcas.common.domain.entity.ResultMetaColumn;
import com.dcas.common.model.dto.MetaColumnPlusDTO;
import com.dcas.common.model.dto.MetaTableDTO;
import com.dcas.common.model.param.DataSourceLimitParam;
import com.dcas.common.model.param.TableDataSetParam;
import com.dcas.common.model.vo.ResultSetVO;
import com.dcas.common.model.vo.TableVO;
import com.dcas.discovery.service.DatabaseService;
import com.dcas.discovery.task.JobLogManager;
import com.dcas.discovery.util.LRUAutoCloseCache;
import com.dcas.discovery.util.cache.ListCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mchz.mcdatasource.api.model.CustomMutableSchema;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mongodb.*;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.mongodb.internal.connection.ServerAddressHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.BsonDocument;
import org.bson.BsonType;
import org.bson.BsonValue;
import org.bson.Document;
import org.bson.codecs.configuration.CodecRegistry;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2024/3/25 10:26
 * @since 1.7.2
 */
@Slf4j
@Service
public class MongoDbDatabaseServiceImpl implements DatabaseService {
    private final LRUAutoCloseCache<MongoClient> clientCache;

    public MongoDbDatabaseServiceImpl() {
        this.clientCache = new LRUAutoCloseCache<>(3);
    }

    @PreDestroy
    public void destroy() {
        clientCache.clear();
    }

    @Override
    public int priority() {
        return 3;
    }

    @Override
    public boolean crawlMetadata(DataSourceLimitParam source, JobLogManager jobLog, TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        if (!DataBaseType.MONGODB.equals(source.getDataBaseType()))
            return false;
        try (MongoClient mongoClient = getMongoClient(source)) {
            //连接到数据库
            Set<String> schemaSet;
            if (null == source.getSchemaSet()) {
                if (null == source.getDbName()) {
                    schemaSet = Sets.newHashSet(getSchemas(mongoClient));
                } else {
                    schemaSet = Collections.singleton(source.getDbName());
                }
            } else {
                schemaSet = source.getSchemaSet();
            }
            jobLog.processed(schemaSet.size());
            for (String database : schemaSet) {
                MongoDatabase db;
                try {
                    db = mongoClient.getDatabase(database);
                } catch (IllegalArgumentException e) {
                    continue;
                }
                CodecRegistry registry = db.getCodecRegistry();

                MongoIterable<String> mongoIterable = db.listCollectionNames();
                for (String collection : mongoIterable) {
                    try {
                        MongoCollection<Document> table = db.getCollection(collection);
                        FindIterable<Document> iterable = table.find().limit(Integer.MAX_VALUE);
                        Map<String, ColumnInfo> columnMap = Maps.newHashMap();
                        for (Document document : iterable) {
                            BsonDocument bsonDocument = document.toBsonDocument(null, registry);
                            for (Map.Entry<String, BsonValue> entry : bsonDocument.entrySet()) {
                                ColumnInfo info = columnMap.computeIfAbsent(entry.getKey(), ColumnInfo::new);
                                info.mayBeType(entry.getValue().getBsonType());
                            }
                        }
                        List<MetaColumnPlusDTO> list = columnMap.values().stream().map(this::buildColumn)
                                .collect(Collectors.toList());

                        MetaTableExpand tableExpand = new MetaTableExpand();
                        tableExpand.setSchemaName(database);
                        tableExpand.setTableName(collection);

                        consumer.accept(buildTable(table), list, tableExpand);
                        jobLog.complete();
                    } catch (Exception e) {
                        log.error("获取元数据失败" + database + "." + collection, e);
                        throw e;
                    }
                }
            }
        }
        return true;
    }

    @Override
    public boolean crawlViewMetadata(DataSourceLimitParam source, JobLogManager jobLog, TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        return false;
    }

    @Override
    public List<String> crawlSchema(DataSourceParam config) {
        if (!DataBaseType.MONGODB.equals(config.getDataBaseType())) {
            return null;
        }
        try (MongoClient mongoClient = getMongoClient(config)) {
            return getSchemas(mongoClient);
        }
    }

    @Override
    public List<TableVO> getTables(DataSourceParam config) {
        if (!DataBaseType.MONGODB.equals(config.getDataBaseType())) {
            return null;
        }
        return new ArrayList<>();
    }

    @Override
    public List<CustomMutableSchema> getTableColumns(DataSourceParam config) {
        if (!DataBaseType.MONGODB.equals(config.getDataBaseType())) {
            return null;
        }
        return new ArrayList<>();
    }

    @Override
    public boolean testConnection(DataSourceParam config) {
        if (!DataBaseType.MONGODB.equals(config.getDataBaseType())) {
            return false;
        }
        try (MongoClient client = getMongoClient(config)) {
            MongoIterable<String> names = client.listDatabaseNames();
            log.debug(names.first());
            return true;
        } catch (MongoClientException e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public boolean testQuery(DataSourceParam config) {
        return false;
    }

    @Override
    public ResultSetVO getTableDataSet(TableDataSetParam param, boolean close) throws Exception {
        DataSourceParam config = param.getSource();
        if (!DataBaseType.MONGODB.equals(config.getDataBaseType())) {
            return null;
        }
        MongoClient mongo;
        if (close) {
            mongo = getMongoClient(config);
        } else {
            mongo = clientCache.get(config.getId(), () -> getMongoClient(config));
        }
        try {
            return getTableData(mongo, param);
        } finally {
            if (close) {
                IoUtil.close(mongo);
            }
        }
    }

    @Override
    public ClassLoader getTableDataSet2Cache(TableDataSetParam param, ListCache<ColumnDataDTO> resultDataCache, ListCache<ColumnName> resultNameCache, boolean close) throws Exception {
        return null;
    }

    private MetaTableDTO buildTable(MongoCollection<Document> table) {
        MetaTableDTO tableDisplay = new MetaTableDTO();
        MongoNamespace namespace = table.getNamespace();
        tableDisplay.setSchema(namespace.getDatabaseName());
        tableDisplay.setTableName(namespace.getCollectionName());
        tableDisplay.setIsView(false);
        return tableDisplay;
    }

    private MetaColumnPlusDTO buildColumn(ColumnInfo columnInfo) {
        MetaColumnPlusDTO columnDisplay = new MetaColumnPlusDTO();
        columnDisplay.setColumnName(columnInfo.getName());
        columnDisplay.setColumnType(columnInfo.getColumnType());
        return columnDisplay;
    }

    private List<String> getSchemas(MongoClient mongoClient) {
        List<String> ret = Lists.newArrayList();
        for (String databaseName : mongoClient.listDatabaseNames()) {
            ret.add(databaseName);
        }
        return ret;
    }

    private MongoClient getMongoClient(DataSourceParam config) {
        ServerAddress address = ServerAddressHelper.createServerAddress(config.getHost(), Integer.parseInt(config.getPort()));
        MongoClientOptions options = MongoClientOptions.builder().build();
        Map<String, String> map = config.getAttachment();
        Authentication auth = Authentication.getType(MapUtil.getStr(map, MetaConst.AUTH));
        MongoCredential credential;
        switch (auth) {
            case PASSWORD:
                credential = MongoCredential.createCredential(config.getUsername(),
                        ObjectUtil.defaultIfNull(config.getDbName(), "admin"), config.getPassword().toCharArray());
                break;
            case LDAP:
                credential = MongoCredential.createPlainCredential(config.getUsername(),
                        ObjectUtil.defaultIfNull(config.getDbName(), "$external"), config.getPassword().toCharArray());
                break;
            case X509:
                credential = MongoCredential.createMongoX509Credential();
                break;
            default:
                return new MongoClient(address, options);
        }
        return new MongoClient(address, credential, options);
    }

    /**
     * 假设 同字段名的数据类型相同
     */
    private ResultSetVO getTableData(MongoClient mongoClient, TableDataSetParam param) {
        MongoDatabase db = mongoClient.getDatabase(param.getSchema());
        MongoCollection<Document> table = db.getCollection(param.getTable());
        FindIterable<Document> iterable = table.find().limit(param.getSize());
        ResultSetVO vo = new ResultSetVO();
        vo.setMetadata(param.getColumns().stream().map(ResultMetaColumn::getColumnName).collect(Collectors.toList()));
        List<List<Object>> data = Lists.newArrayListWithCapacity(param.getSize());
        for (Document document : iterable) {
            List<Object> row = vo.getMetadata().stream().map(document::get).collect(Collectors.toList());
            data.add(row);
        }
        vo.setData(data);
        return vo;
    }

    private static class ColumnInfo {
        @Getter
        private final String name;

        private final Map<BsonType, Counter> typeCounter;
        private ColumnType columnType;

        public ColumnInfo(String name) {
            this.name = name;
            this.typeCounter = Maps.newEnumMap(BsonType.class);
        }

        public void mayBeType(BsonType type) {
            Counter counter = typeCounter.computeIfAbsent(type, v -> Counter.init());
            counter.add();
        }

        public ColumnType getColumnType() {
            if (null == columnType && !typeCounter.isEmpty()) {
                Optional<Map.Entry<BsonType, Counter>> max = typeCounter.entrySet().stream()
                        .max((Map.Entry.comparingByValue()));
                if (max.isPresent()) {
                    BsonType type = max.get().getKey();
                    this.columnType = new ColumnType(type.name(), getTypeGroup(type));
                }
            }
            return columnType;
        }

        private DataTypeGroup getTypeGroup(BsonType type) {
            if (null == type) {
                return null;
            }
            switch (type) {
                case BINARY:
                    return DataTypeGroup.BINARY;
                case BOOLEAN:
                    return DataTypeGroup.BIT;
                case STRING:
                case DOCUMENT:
                case END_OF_DOCUMENT:
                case REGULAR_EXPRESSION:
                    return DataTypeGroup.CHARACTER;
                case OBJECT_ID:
                    return DataTypeGroup.ID;
                case INT32:
                case INT64:
                    return DataTypeGroup.INTEGER;
                case DOUBLE:
                case DECIMAL128:
                    return DataTypeGroup.REAL;
                case DATE_TIME:
                case TIMESTAMP:
                    return DataTypeGroup.TEMPORAL;
                case SYMBOL:
                case MAX_KEY:
                case MIN_KEY:
                case ARRAY:
                case DB_POINTER:
                    return DataTypeGroup.OBJECT;
                case JAVASCRIPT:
                case JAVASCRIPT_WITH_SCOPE:
                    return DataTypeGroup.LARGE_OBJECT;
                default:
                    return DataTypeGroup.UNKNOWN;
            }
        }
    }
}
