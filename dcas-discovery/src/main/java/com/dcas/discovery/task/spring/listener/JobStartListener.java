package com.dcas.discovery.task.spring.listener;

import cn.hutool.core.io.FileUtil;
import com.dcas.common.enums.JobType;
import com.dcas.common.exception.ServiceException;
import com.dcas.discovery.domain.enums.Status;
import com.dcas.discovery.service.DiscoveryJobService;
import com.dcas.discovery.task.spring.event.JobStartEvent;
import com.dcas.discovery.task.spring.payload.JobStartPayload;
import com.mchz.base.metis.common.MetisConst;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <p>
 *
 * </p>
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class JobStartListener {
    private final DiscoveryJobService discoveryJobService;

    @Async("taskExecutor")
    @TransactionalEventListener(value = JobStartEvent.class, fallbackExecution = true)
    public void processComplete(JobStartEvent event) {
        JobStartPayload start = (JobStartPayload) event.getSource();
        if (null == start.getJobType()) {
            throw new ServiceException(Status.PARAM_NOT_NULL);
        }
        // 若分类分级得引擎包metis不存在，则作业类型改为0
        if (FileUtil.exist(System.getProperty(MetisConst.APP_HOME))){
            start.setJobType(JobType.DISCOVERY_DARK);
        } else {
            start.setJobType(JobType.DISCOVERY);
        }
        switch (start.getJobType()) {
            case DISCOVERY:
                discoveryJobService.autoStartJob();
                break;
            case DISCOVERY_DARK:
                discoveryJobService.autoStartDarkJob();
                break;
            default:
        }
    }
}
