package com.dcas.discovery.engine.rule;


import com.dcas.discovery.engine.EngineContext;

public class AmountRule extends TechRule {
    public static final String TYPE = "金额";

    public AmountRule(Long bizId, String bizName) {
        super(bizId, bizName);
    }

    @Override
    public TechRuleResult process(EngineContext context) {
        String name = context.getPayload().getType().getName();
        if ("DECIMAL".equalsIgnoreCase(name)) {
            return new TechRuleResult(true, getType(), null);
        }
        return new TechRuleResult(false, null, null);
    }

    @Override
    protected String getType() {
        return TYPE;
    }
}
