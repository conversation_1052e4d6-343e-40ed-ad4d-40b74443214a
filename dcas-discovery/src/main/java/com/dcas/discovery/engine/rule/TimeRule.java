package com.dcas.discovery.engine.rule;

import com.dcas.discovery.engine.EngineContext;
import com.mchz.mcdatasource.api.model.CustomColumnTypeImpl;

public class TimeRule extends TechRule {
    public static final String TYPE = "时间";

    public TimeRule(Long bizId, String bizName) {
        super(bizId, bizName);
    }

    @Override
    public TechRuleResult process(EngineContext context) {
        String name = context.getPayload().getType().getName();
        if (CustomColumnTypeImpl.valueOf(name).isTimeBased()) {
            return new TechRuleResult(true, getType(), null);
        }
        return new TechRuleResult(false, null, null);
    }

    @Override
    protected String getType() {
        return TYPE;
    }
}
