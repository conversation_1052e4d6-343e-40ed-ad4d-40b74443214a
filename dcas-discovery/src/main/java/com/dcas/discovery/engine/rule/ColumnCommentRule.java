package com.dcas.discovery.engine.rule;

import com.dcas.discovery.engine.EngineContext;
import com.dcas.discovery.engine.rule.match.Match;
import org.apache.commons.lang3.StringUtils;

public class ColumnCommentRule extends BusinessRule<ColumnCommentRule.ColumnCommentRuleResult> {

    public ColumnCommentRule(Long bizId, String bizName, int priority, int degree, Long ruleId,
                             Match<Boolean> match) {
        super(bizId, bizName, priority, degree, ruleId, match);
    }

    @Override
    public ColumnCommentRuleResult process(EngineContext context) {
        String name = context.getPayload().getComment();
        boolean match = StringUtils.isNotEmpty(name) && ((Match<Boolean>) super.match).check(name);
        return new ColumnCommentRuleResult(match);
    }

    public class ColumnCommentRuleResult extends BusinessRule<ColumnCommentRuleResult>.BusinessRuleResult {
        public ColumnCommentRuleResult(boolean match) {
            super(match);
        }
    }
}
